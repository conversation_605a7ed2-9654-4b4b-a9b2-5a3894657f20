from flask import Blueprint, jsonify, current_app, request
from flaskr.helpers.auth import _verify_claims, get_token, ptt_powerbi_admin_required, user_required
from flaskr.services.powerbi_service import powerbi_service
from flaskr.services.powerbi_data_service import powerbi_data_service
from flaskr.services.powerbi_automation_config import powerbi_automation_config
from celery_app.tasks.powerbi_automation import refresh_powerbi_reports_task, refresh_single_report_task, monitor_powerbi_refresh_status_task
from jose import jwt
import traceback

powerbi_api = Blueprint("powerbi_api", __name__)


@powerbi_api.route("/dashboard/embed-info", methods=["GET"])
def get_dashboard_embed_info():
    """
    Get PowerBI dashboard embed information
    """
    try:
        try:
            access_token = get_token(request.headers.get("Authorization"))
            claims = jwt.get_unverified_claims(access_token)
            user_id = claims["sub"]
        except Exception as e:
            return jsonify({"error": f"Authentication error: {str(e)}"}), 500
        # Check if ERV-specific report is requested
        report_type = request.args.get("report_type")
        if report_type == "erv":
            try:
                has_erv_access = _verify_claims(access_token, ["ptt-erv-dashboard"])
                has_admin_access = _verify_claims(access_token, ["ptt-admin"])
                has_powerbi_admin_access = _verify_claims(access_token, ["ptt-powerbi-admin"])
                if not (has_erv_access or has_admin_access or has_powerbi_admin_access):
                    return jsonify(msg="ERV dashboard access restricted to ERV users and admins"), 403

                workspace_id = current_app.config.get("POWERBI_WORKSPACE_ID")
                # Get ERV dashboard configuration from database
                erv_config = powerbi_service.get_erv_powerbi_dashboard_config()
                if erv_config:
                    report_id = erv_config["report_id"]
                else:
                    # Fallback to environment config if database config not found
                    report_id = current_app.config.get("POWERBI_ERV_REPORT_ID")
            except Exception as e:
                return jsonify({"error": f"Authorization error: {str(e)}"}), 500
        else:
            try:
                # has_powerbi_admin = _verify_claims(access_token, ["ptt-powerbi-admin"])
                # if not has_powerbi_admin:
                #     return jsonify(msg="PowerBI dashboard access restricted to PowerBI admins"), 403

                workspace_id = current_app.config.get("POWERBI_WORKSPACE_ID")
                report_id = request.args.get("reportId")
                if report_id is None:
                    report_id = current_app.config.get("POWERBI_REPORT_ID")
            except Exception as e:
                return jsonify({"error": f"Authorization error: {str(e)}"}), 500

        current_app.logger.info(f"Using workspace_id: {workspace_id}, report_id: {report_id}")

        if not workspace_id or not report_id:
            error_msg = "PowerBI configuration not found"
            current_app.logger.error(error_msg)
            return jsonify({"error": error_msg}), 500

        # Get report details and embed token
        try:
            current_app.logger.info("Getting report embed info...")
            embed_info = powerbi_service.get_report_embed_info(report_id, workspace_id, user_id)

            return jsonify(
                {
                    "embedUrl": embed_info.get("embedUrl"),
                    "embedToken": embed_info.get("token"),
                    "reportId": report_id,
                    "workspaceId": workspace_id,
                }
            )
        except Exception as e:
            current_app.logger.error(f"Error getting embed info: {str(e)}")
            return jsonify({"error": f"Error getting PowerBI embed info: {str(e)}"}), 500

    except Exception as e:
        error_msg = f"Error getting PowerBI dashboard info: {str(e)}\n{traceback.format_exc()}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/reports", methods=["GET"])
@ptt_powerbi_admin_required()
def get_workspace_reports(access_token):
    """
    Get all reports available in the PowerBI workspace
    """
    try:
        workspace_id = request.args.get("workspace_id") or current_app.config.get("POWERBI_WORKSPACE_ID")
        if not workspace_id:
            error_msg = "PowerBI workspace ID not provided and not configured"
            return jsonify({"error": error_msg}), 400

        # Get all reports in the workspace
        reports = powerbi_service.get_workspace_reports(workspace_id)
        return jsonify({"reports": reports})
    except Exception as e:
        error_msg = f"Error getting PowerBI reports: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/assign-powerbi-reports", methods=["PUT"])
@ptt_powerbi_admin_required()
def update_user_powerbi_reports(access_token):
    """
    Assign or remove PowerBI reports for a user
    """
    try:
        data = request.json
        if not data.get("userId") or not isinstance(data.get("pbiReports"), list):
            return jsonify({"error": "Missing userId or invalid pbiReports format"}), 400
            
        user_id = data["userId"]
        pbi_reports = data["pbiReports"]
        
        # Update user's PowerBI reports in database
        result = powerbi_service.update_user_powerbi_reports(user_id, pbi_reports)
        return jsonify({"success": True, "message": "User PowerBI reports updated successfully"}), 200
    except Exception as e:
        error_msg = f"Error updating user PowerBI reports: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/assigned-powerbi-reports/<user_id>", methods=["GET"])
@ptt_powerbi_admin_required()
def get_user_powerbi_reports(access_token, user_id):
    """
    Get PowerBI reports assigned to a user
    """
    try:
        if not user_id:
            return jsonify({"error": "User ID is required"}), 400
            
        # Get user's PowerBI reports from database
        reports = powerbi_service.get_user_powerbi_reports(user_id)
        return jsonify({"reports": reports}), 200
    except Exception as e:
        error_msg = f"Error retrieving user PowerBI reports: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/assigned-powerbi-reports", methods=["GET"])
@user_required()
def get_my_powerbi_reports(access_token):
    """
    Get PowerBI reports assigned to the logged-in user
    """
    try:
        claims = jwt.get_unverified_claims(access_token)
        user_id = claims["sub"]
            
        # Get user's PowerBI reports from database
        reports = powerbi_service.get_user_powerbi_reports(user_id)
        return jsonify({"reports": reports}), 200
    except Exception as e:
        error_msg = f"Error retrieving user PowerBI reports: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500

@powerbi_api.route("/erv-powerbi-dashboard-config", methods=["PUT"])
@ptt_powerbi_admin_required()
def update_erv_dashboard_config(access_token):
    """
    Update ERV Powerbi dashboard configuration
    """
    try:
        data = request.json
        if not data.get("reportId") or not data.get("reportName"):
            return jsonify({"error": "Missing reportId or reportName"}), 400
            
        report_id = data["reportId"]
        report_name = data["reportName"]
        
        # Update ERV dashboard configuration in database
        powerbi_service.update_erv_dashboard_config(report_id, report_name)
        return jsonify({"success": True, "message": "ERV Powerbi dashboard configuration updated successfully"}), 200
    except Exception as e:
        error_msg = f"Error updating ERV dashboard configuration: {str(e)}"
        return jsonify({"error": error_msg}), 500

@powerbi_api.route("/erv-powerbi-dashboard-config", methods=["GET"])
@ptt_powerbi_admin_required()
def get_erv_powerbi_dashboard_config(access_token):
    try:
        # Get ERV dashboard configuration from database
        config = powerbi_service.get_erv_powerbi_dashboard_config()
        return jsonify({"config": config}), 200
    except Exception as e:
        error_msg = f"Error retrieving ERV dashboard configuration: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


# PowerBI Automation Endpoints

@powerbi_api.route("/automation/refresh-all", methods=["POST"])
@ptt_powerbi_admin_required()
def trigger_all_reports_refresh(access_token):
    """
    Trigger refresh for all configured PowerBI reports
    """
    try:
        data = request.json or {}

        # Build configuration for the automation task
        config = {
            "report_ids": data.get("report_ids", []),  # Empty list will use defaults
            "client_ids": data.get("client_ids"),
            "days_back": data.get("days_back", 30),
            "refresh_type": data.get("refresh_type", "full"),  # 'dataset_only', 'data_push', or 'full'
            "workspace_id": data.get("workspace_id")
        }

        # Trigger the Celery task
        task = refresh_powerbi_reports_task.delay(config)

        current_app.logger.info(f"Triggered PowerBI automation task: {task.id}")

        return jsonify({
            "message": "PowerBI reports refresh initiated",
            "task_id": task.id,
            "config": config
        }), 202

    except Exception as e:
        error_msg = f"Error triggering PowerBI automation: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/automation/refresh-report", methods=["POST"])
@ptt_powerbi_admin_required()
def trigger_single_report_refresh(access_token):
    """
    Trigger refresh for a specific PowerBI report
    """
    try:
        data = request.json
        if not data or not data.get("report_id"):
            return jsonify({"error": "report_id is required"}), 400

        report_id = data["report_id"]
        workspace_id = data.get("workspace_id")
        client_ids = data.get("client_ids")
        days_back = data.get("days_back", 30)

        # Trigger the Celery task
        task = refresh_single_report_task.delay(
            report_id=report_id,
            workspace_id=workspace_id,
            client_ids=client_ids,
            days_back=days_back
        )

        current_app.logger.info(f"Triggered single report refresh task: {task.id} for report: {report_id}")

        return jsonify({
            "message": f"PowerBI report {report_id} refresh initiated",
            "task_id": task.id,
            "report_id": report_id
        }), 202

    except Exception as e:
        error_msg = f"Error triggering single report refresh: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/automation/monitor-dataset", methods=["POST"])
@ptt_powerbi_admin_required()
def monitor_dataset_refresh(access_token):
    """
    Monitor the refresh status of a PowerBI dataset
    """
    try:
        data = request.json
        if not data or not data.get("dataset_id"):
            return jsonify({"error": "dataset_id is required"}), 400

        dataset_id = data["dataset_id"]
        workspace_id = data.get("workspace_id")
        max_wait_minutes = data.get("max_wait_minutes", 30)

        # Trigger the monitoring task
        task = monitor_powerbi_refresh_status_task.delay(
            dataset_id=dataset_id,
            workspace_id=workspace_id,
            max_wait_minutes=max_wait_minutes
        )

        current_app.logger.info(f"Started dataset monitoring task: {task.id} for dataset: {dataset_id}")

        return jsonify({
            "message": f"Dataset {dataset_id} monitoring initiated",
            "task_id": task.id,
            "dataset_id": dataset_id
        }), 202

    except Exception as e:
        error_msg = f"Error starting dataset monitoring: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/datasets", methods=["GET"])
@ptt_powerbi_admin_required()
def get_workspace_datasets(access_token):
    """
    Get all datasets in the PowerBI workspace
    """
    try:
        workspace_id = request.args.get("workspace_id") or current_app.config.get("POWERBI_WORKSPACE_ID")
        if not workspace_id:
            return jsonify({"error": "workspace_id is required"}), 400

        datasets = powerbi_service.get_datasets(workspace_id)
        return jsonify({"datasets": datasets}), 200

    except Exception as e:
        error_msg = f"Error getting datasets: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/datasets/<dataset_id>/refresh-history", methods=["GET"])
@ptt_powerbi_admin_required()
def get_dataset_refresh_history(access_token, dataset_id):
    """
    Get refresh history for a specific dataset
    """
    try:
        workspace_id = request.args.get("workspace_id") or current_app.config.get("POWERBI_WORKSPACE_ID")
        top = int(request.args.get("top", 10))

        refresh_history = powerbi_service.get_refresh_history(dataset_id, workspace_id, top)
        return jsonify({"refresh_history": refresh_history}), 200

    except Exception as e:
        error_msg = f"Error getting refresh history: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/data/preview", methods=["GET"])
@ptt_powerbi_admin_required()
def preview_data_for_powerbi(access_token):
    """
    Preview the data that would be sent to PowerBI (for testing)
    """
    try:
        client_ids = request.args.getlist("client_ids") or None
        days_back = int(request.args.get("days_back", 30))
        data_type = request.args.get("type", "summary")  # 'banking', 'claims', or 'summary'
        limit = int(request.args.get("limit", 100))

        if data_type == "banking":
            data = powerbi_data_service.get_latest_banking_data(client_ids, days_back, limit)
        elif data_type == "claims":
            data = powerbi_data_service.get_latest_claims_data(client_ids, days_back, limit)
        else:  # summary
            data = powerbi_data_service.get_combined_summary_data(client_ids, days_back)
            if limit and len(data) > limit:
                data = data[:limit]

        return jsonify({
            "data_type": data_type,
            "record_count": len(data),
            "days_back": days_back,
            "preview": data
        }), 200

    except Exception as e:
        error_msg = f"Error previewing data: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


# PowerBI Automation Configuration Endpoints

@powerbi_api.route("/automation/config", methods=["GET"])
@ptt_powerbi_admin_required()
def get_automation_config(access_token):
    """
    Get PowerBI automation configuration
    """
    try:
        config = powerbi_automation_config.get_automation_config()
        return jsonify({"config": config}), 200
    except Exception as e:
        error_msg = f"Error getting automation config: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/automation/config", methods=["PUT"])
@ptt_powerbi_admin_required()
def update_automation_config(access_token):
    """
    Update PowerBI automation configuration
    """
    try:
        config = request.json
        if not config:
            return jsonify({"error": "Configuration data is required"}), 400

        powerbi_automation_config.update_automation_config(config)
        return jsonify({"message": "Automation configuration updated successfully"}), 200
    except Exception as e:
        error_msg = f"Error updating automation config: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/automation/history", methods=["GET"])
@ptt_powerbi_admin_required()
def get_automation_history(access_token):
    """
    Get PowerBI automation run history
    """
    try:
        limit = int(request.args.get("limit", 50))
        status_filter = request.args.get("status")

        history = powerbi_automation_config.get_automation_history(limit, status_filter)
        return jsonify({"history": history}), 200
    except Exception as e:
        error_msg = f"Error getting automation history: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/automation/stats", methods=["GET"])
@ptt_powerbi_admin_required()
def get_automation_stats(access_token):
    """
    Get PowerBI automation statistics
    """
    try:
        days_back = int(request.args.get("days_back", 30))

        stats = powerbi_automation_config.get_automation_stats(days_back)
        return jsonify({"stats": stats}), 200
    except Exception as e:
        error_msg = f"Error getting automation stats: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500


@powerbi_api.route("/automation/cleanup-logs", methods=["POST"])
@ptt_powerbi_admin_required()
def cleanup_automation_logs(access_token):
    """
    Clean up old automation logs
    """
    try:
        days_to_keep = int(request.json.get("days_to_keep", 90)) if request.json else 90

        deleted_count = powerbi_automation_config.cleanup_old_logs(days_to_keep)
        return jsonify({
            "message": f"Cleaned up {deleted_count} old automation logs",
            "deleted_count": deleted_count
        }), 200
    except Exception as e:
        error_msg = f"Error cleaning up logs: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({"error": error_msg}), 500
