from flask import current_app
import msal
import requests

# from datetime import datetime, timedelta
from flaskr.models import get_db
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)


class PowerBIService:
    def __init__(self):
        self._client_id = None
        self._client_secret = None
        self._tenant_id = None
        self._authority = None
        self._workspace_id = None
        self.scope = ["https://analysis.windows.net/powerbi/api/.default"]
        self.powerbi_api_url = "https://api.powerbi.com/v1.0/myorg"

    @property
    def client_id(self):
        if self._client_id is None:
            self._client_id = current_app.config.get("POWERBI_CLIENT_ID")
        return self._client_id

    @property
    def client_secret(self):
        if self._client_secret is None:
            self._client_secret = current_app.config.get("POWERBI_CLIENT_SECRET")
        return self._client_secret

    @property
    def tenant_id(self):
        if self._tenant_id is None:
            self._tenant_id = current_app.config.get("POWERBI_TENANT_ID")
        return self._tenant_id

    @property
    def authority(self):
        if self._authority is None:
            self._authority = f"https://login.microsoftonline.com/{self.tenant_id}"
        return self._authority

    @property
    def workspace_id(self):
        if self._workspace_id is None:
            self._workspace_id = current_app.config.get("POWERBI_WORKSPACE_ID")
        return self._workspace_id

    def get_access_token(self):
        """Get PowerBI access token using client credentials flow"""

        app = msal.ConfidentialClientApplication(
            self.client_id, authority=self.authority, client_credential=self.client_secret
        )

        result = app.acquire_token_for_client(scopes=self.scope)
        if "access_token" in result:
            current_app.logger.info("Successfully obtained access token")
            return result["access_token"]
        else:
            error_msg = f"Failed to get access token. Error: {result.get('error')}, Description: {result.get('error_description')}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

    def get_report_embed_info(self, report_id, workspace_id, user_id):
        current_app.logger.info(
            f"Getting report embed info for report_id: {report_id}, workspace_id: {workspace_id}, user_id: {user_id}"
        )
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}"}
        # Get report details to get embed URL
        details_url = f"{self.powerbi_api_url}/groups/{workspace_id}/reports/{report_id}"
        details_response = requests.get(details_url, headers=headers)

        if details_response.status_code != 200:
            try:
                error_details = details_response.json()
                error_msg = f"Failed to get report details. Status: {details_response.status_code}, Error: {json.dumps(error_details)}"
            except json.JSONDecodeError:
                error_msg = f"Failed to get report details. Status: {details_response.status_code}, Response: {details_response.text}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

        report_details = details_response.json()
        current_app.logger.info("Successfully obtained report details")

        # Get embed token
        token_url = f"{self.powerbi_api_url}/groups/{workspace_id}/reports/{report_id}/GenerateToken"
        embed_token_request = {"accessLevel": "View", "allowSaveAs": False}
        token_headers = {**headers, "Content-Type": "application/json"}
        token_response = requests.post(token_url, headers=token_headers, json=embed_token_request)

        if token_response.status_code != 200:
            current_app.logger.error(f"Failed to get embed token. Status: {token_response.status_code}")
            raise Exception(
                f"Failed to get embed token. Status: {token_response.status_code}, Response: {token_response.text}"
            )

        embed_token = token_response.json()
        current_app.logger.info("Successfully obtained embed token")
        return {
            "embedUrl": report_details.get("embedUrl"),
            "token": embed_token.get("token"),
            "reportId": report_id,
            "workspaceId": workspace_id,
        }

    def get_workspace_reports(self, workspace_id=None):
        workspace_id = workspace_id or self.workspace_id
        current_app.logger.info(f"Getting reports for workspace: {workspace_id}")
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}"}

        # Get all reports in the workspace
        reports_url = f"{self.powerbi_api_url}/groups/{workspace_id}/reports"
        response = requests.get(reports_url, headers=headers)
        if response.status_code != 200:
            try:
                error_details = response.json()
                error_msg = f"Failed to get workspace reports. Status: {response.status_code}, Error: {json.dumps(error_details)}"
            except:
                error_msg = (
                    f"Failed to get workspace reports. Status: {response.status_code}, Response: {response.text}"
                )
            current_app.logger.error(error_msg)
            raise Exception(error_msg)
        reports_data = response.json()
        current_app.logger.info(f"Successfully retrieved {len(reports_data.get('value', []))} reports")

        reports = [{"id": report["id"], "name": report["name"]} for report in reports_data.get("value", [])]
        return reports
    
    def update_user_powerbi_reports(self, user_id, pbi_reports):
        """
        Update PowerBI reports assigned to a user
        
        Args:
            user_id (str): The user ID
            pbi_reports (list): List of PowerBI report objects with id and name
            
        Returns:
            bool: True if successful
        """
        current_app.logger.info(f"Updating PowerBI reports for user: {user_id}")
    
        # Update user document in database with PowerBI reports
        result = get_db().user.update_one(
            {"user_id": user_id},
            {"$set": {"powerbi_reports": pbi_reports}}
        )
        
        if result.matched_count == 0:
            current_app.logger.error(f"User with ID {user_id} not found")
            raise Exception(f"User with ID {user_id} not found")
        
        current_app.logger.info(f"Successfully updated PowerBI reports for user: {user_id}")
        return True

    def get_user_powerbi_reports(self, user_id):
        """
        Get PowerBI reports assigned to a user
        
        Args:
            user_id (str): The user ID
            
        Returns:
            list: List of PowerBI report objects with id and name
        """
        current_app.logger.info(f"Getting PowerBI reports for user: {user_id}")
        
        # Get user document from database
        user = get_db().user.find_one({"user_id": user_id}, {"powerbi_reports": 1})
        
        if not user:
            current_app.logger.error(f"User with ID {user_id} not found")
            raise Exception(f"User with ID {user_id} not found")
        
        # Return empty list if no reports are assigned
        reports = user.get("powerbi_reports", [])
        current_app.logger.info(f"Found {len(reports)} PowerBI reports for user: {user_id}")
        return reports

    def get_erv_powerbi_dashboard_config(self):
        """
        Get ERV dashboard configuration from database
        """
        config = get_db().erv_powerbi_config.find_one()
        
        if not config:
            # Return default configuration from environment if not found in database
            default_report_id = current_app.config.get("POWERBI_ERV_REPORT_ID")
            if default_report_id:
                return {
                    "report_id": default_report_id,
                    "name": "ERV Dashboard"
                }
            else:
                current_app.logger.warning("No ERV dashboard configuration found")
                return None
        
        current_app.logger.info(f"Found ERV dashboard configuration: {config.get('report_id')}")
        return {
            "report_id": config.get("report_id"),
            "name": config.get("name", "ERV Dashboard")
        }

    def update_erv_dashboard_config(self, report_id, report_name):
        """
        Update ERV dashboard configuration in database
        """
        current_app.logger.info(f"Updating ERV dashboard configuration: {report_id}")

        # Update or insert ERV dashboard configuration
        get_db().erv_powerbi_config.update_one(
            {},
            {
                "$set": {
                    "report_id": report_id,
                    "name": report_name,
                    "updated_at": datetime.utcnow()
                }
            },
            upsert=True
        )
        current_app.logger.info(f"Successfully updated ERV Powerbi dashboard configuration: {report_id}")
        return True

    def get_datasets(self, workspace_id=None):
        """
        Get all datasets in the PowerBI workspace

        Args:
            workspace_id (str): PowerBI workspace ID

        Returns:
            list: List of datasets with id, name, and other properties
        """
        workspace_id = workspace_id or self.workspace_id
        current_app.logger.info(f"Getting datasets for workspace: {workspace_id}")
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}"}

        datasets_url = f"{self.powerbi_api_url}/groups/{workspace_id}/datasets"
        response = requests.get(datasets_url, headers=headers)

        if response.status_code != 200:
            try:
                error_details = response.json()
                error_msg = f"Failed to get datasets. Status: {response.status_code}, Error: {json.dumps(error_details)}"
            except json.JSONDecodeError:
                error_msg = f"Failed to get datasets. Status: {response.status_code}, Response: {response.text}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

        datasets_data = response.json()
        current_app.logger.info(f"Successfully retrieved {len(datasets_data.get('value', []))} datasets")
        return datasets_data.get('value', [])

    def get_report_datasets(self, report_id, workspace_id=None):
        """
        Get datasets used by a specific report

        Args:
            report_id (str): PowerBI report ID
            workspace_id (str): PowerBI workspace ID

        Returns:
            list: List of dataset IDs used by the report
        """
        workspace_id = workspace_id or self.workspace_id
        current_app.logger.info(f"Getting datasets for report: {report_id}")
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}"}

        # Get report details to find associated datasets
        report_url = f"{self.powerbi_api_url}/groups/{workspace_id}/reports/{report_id}"
        response = requests.get(report_url, headers=headers)

        if response.status_code != 200:
            try:
                error_details = response.json()
                error_msg = f"Failed to get report details. Status: {response.status_code}, Error: {json.dumps(error_details)}"
            except json.JSONDecodeError:
                error_msg = f"Failed to get report details. Status: {response.status_code}, Response: {response.text}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

        report_data = response.json()
        dataset_id = report_data.get('datasetId')

        if dataset_id:
            current_app.logger.info(f"Found dataset {dataset_id} for report {report_id}")
            return [dataset_id]
        else:
            current_app.logger.warning(f"No dataset found for report {report_id}")
            return []

    def refresh_dataset(self, dataset_id, workspace_id=None):
        """
        Trigger a refresh of a PowerBI dataset

        Args:
            dataset_id (str): PowerBI dataset ID
            workspace_id (str): PowerBI workspace ID

        Returns:
            dict: Refresh operation details
        """
        workspace_id = workspace_id or self.workspace_id
        current_app.logger.info(f"Triggering refresh for dataset: {dataset_id}")
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}

        refresh_url = f"{self.powerbi_api_url}/groups/{workspace_id}/datasets/{dataset_id}/refreshes"

        # Trigger refresh
        response = requests.post(refresh_url, headers=headers, json={})

        if response.status_code not in [200, 202]:
            try:
                error_details = response.json()
                error_msg = f"Failed to trigger dataset refresh. Status: {response.status_code}, Error: {json.dumps(error_details)}"
            except json.JSONDecodeError:
                error_msg = f"Failed to trigger dataset refresh. Status: {response.status_code}, Response: {response.text}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

        current_app.logger.info(f"Successfully triggered refresh for dataset: {dataset_id}")
        return {"dataset_id": dataset_id, "status": "refresh_triggered", "workspace_id": workspace_id}

    def get_refresh_history(self, dataset_id, workspace_id=None, top=5):
        """
        Get refresh history for a dataset

        Args:
            dataset_id (str): PowerBI dataset ID
            workspace_id (str): PowerBI workspace ID
            top (int): Number of recent refreshes to retrieve

        Returns:
            list: List of refresh operations with status and timestamps
        """
        workspace_id = workspace_id or self.workspace_id
        current_app.logger.info(f"Getting refresh history for dataset: {dataset_id}")
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}"}

        refresh_url = f"{self.powerbi_api_url}/groups/{workspace_id}/datasets/{dataset_id}/refreshes?$top={top}"
        response = requests.get(refresh_url, headers=headers)

        if response.status_code != 200:
            try:
                error_details = response.json()
                error_msg = f"Failed to get refresh history. Status: {response.status_code}, Error: {json.dumps(error_details)}"
            except json.JSONDecodeError:
                error_msg = f"Failed to get refresh history. Status: {response.status_code}, Response: {response.text}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

        refresh_data = response.json()
        current_app.logger.info(f"Successfully retrieved refresh history for dataset: {dataset_id}")
        return refresh_data.get('value', [])

    def push_data_to_dataset(self, dataset_id, table_name, data, workspace_id=None):
        """
        Push data rows to a PowerBI dataset table

        Args:
            dataset_id (str): PowerBI dataset ID
            table_name (str): Name of the table in the dataset
            data (list): List of dictionaries representing rows to push
            workspace_id (str): PowerBI workspace ID

        Returns:
            dict: Push operation result
        """
        workspace_id = workspace_id or self.workspace_id
        current_app.logger.info(f"Pushing {len(data)} rows to dataset {dataset_id}, table {table_name}")
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}

        push_url = f"{self.powerbi_api_url}/groups/{workspace_id}/datasets/{dataset_id}/tables/{table_name}/rows"

        # PowerBI has a limit on rows per request, so we'll batch if necessary
        batch_size = 10000  # PowerBI limit
        total_rows = len(data)

        for i in range(0, total_rows, batch_size):
            batch = data[i:i + batch_size]
            payload = {"rows": batch}

            response = requests.post(push_url, headers=headers, json=payload)

            if response.status_code not in [200, 201]:
                try:
                    error_details = response.json()
                    error_msg = f"Failed to push data batch {i//batch_size + 1}. Status: {response.status_code}, Error: {json.dumps(error_details)}"
                except json.JSONDecodeError:
                    error_msg = f"Failed to push data batch {i//batch_size + 1}. Status: {response.status_code}, Response: {response.text}"
                current_app.logger.error(error_msg)
                raise Exception(error_msg)

            current_app.logger.info(f"Successfully pushed batch {i//batch_size + 1} ({len(batch)} rows)")

        current_app.logger.info(f"Successfully pushed all {total_rows} rows to dataset {dataset_id}")
        return {"dataset_id": dataset_id, "table_name": table_name, "rows_pushed": total_rows}

    def clear_dataset_table(self, dataset_id, table_name, workspace_id=None):
        """
        Clear all rows from a PowerBI dataset table

        Args:
            dataset_id (str): PowerBI dataset ID
            table_name (str): Name of the table in the dataset
            workspace_id (str): PowerBI workspace ID

        Returns:
            dict: Clear operation result
        """
        workspace_id = workspace_id or self.workspace_id
        current_app.logger.info(f"Clearing table {table_name} in dataset {dataset_id}")
        access_token = self.get_access_token()
        headers = {"Authorization": f"Bearer {access_token}"}

        clear_url = f"{self.powerbi_api_url}/groups/{workspace_id}/datasets/{dataset_id}/tables/{table_name}/rows"
        response = requests.delete(clear_url, headers=headers)

        if response.status_code not in [200, 204]:
            try:
                error_details = response.json()
                error_msg = f"Failed to clear table. Status: {response.status_code}, Error: {json.dumps(error_details)}"
            except json.JSONDecodeError:
                error_msg = f"Failed to clear table. Status: {response.status_code}, Response: {response.text}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

        current_app.logger.info(f"Successfully cleared table {table_name} in dataset {dataset_id}")
        return {"dataset_id": dataset_id, "table_name": table_name, "status": "cleared"}

    def update_report_data(self, report_id, banking_data=None, claims_data=None, workspace_id=None):
        """
        Update data for a specific report by refreshing its associated datasets

        Args:
            report_id (str): PowerBI report ID
            banking_data (list): Optional banking data to push before refresh
            claims_data (list): Optional claims data to push before refresh
            workspace_id (str): PowerBI workspace ID

        Returns:
            dict: Update operation result
        """
        workspace_id = workspace_id or self.workspace_id
        current_app.logger.info(f"Updating data for report: {report_id}")

        # Get datasets associated with the report
        dataset_ids = self.get_report_datasets(report_id, workspace_id)

        if not dataset_ids:
            error_msg = f"No datasets found for report {report_id}"
            current_app.logger.error(error_msg)
            raise Exception(error_msg)

        results = []

        for dataset_id in dataset_ids:
            try:
                # If data is provided, push it to the dataset
                if banking_data:
                    self.push_data_to_dataset(dataset_id, "BankingData", banking_data, workspace_id)

                if claims_data:
                    self.push_data_to_dataset(dataset_id, "ClaimsData", claims_data, workspace_id)

                # Trigger dataset refresh
                refresh_result = self.refresh_dataset(dataset_id, workspace_id)
                results.append(refresh_result)

            except Exception as e:
                current_app.logger.error(f"Failed to update dataset {dataset_id}: {str(e)}")
                results.append({"dataset_id": dataset_id, "status": "failed", "error": str(e)})

        current_app.logger.info(f"Completed data update for report {report_id}")
        return {"report_id": report_id, "dataset_updates": results}


powerbi_service = PowerBIService()
