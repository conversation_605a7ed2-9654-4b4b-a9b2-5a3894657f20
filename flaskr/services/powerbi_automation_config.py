from flask import current_app
from flaskr.models import get_db
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class PowerBIAutomationConfig:
    """
    Service for managing PowerBI automation configuration and scheduling
    """
    
    def __init__(self):
        self.db = get_db()
    
    def get_automation_config(self):
        """
        Get PowerBI automation configuration from database
        
        Returns:
            dict: Automation configuration or default config
        """
        config = self.db.powerbi_automation_config.find_one()
        
        if not config:
            # Return default configuration
            return {
                "enabled": False,
                "schedule_type": "daily",  # daily, weekly, manual
                "schedule_time": "02:00",  # UTC time
                "days_back": 30,
                "refresh_type": "full",  # dataset_only, data_push, full
                "report_configs": [
                    {
                        "report_id": current_app.config.get("POWERBI_REPORT_ID"),
                        "name": "Main Dashboard",
                        "enabled": True,
                        "client_ids": None  # None means all clients
                    },
                    {
                        "report_id": current_app.config.get("POWERBI_ERV_REPORT_ID"),
                        "name": "ERV Dashboard", 
                        "enabled": True,
                        "client_ids": None
                    }
                ],
                "notification_settings": {
                    "on_success": False,
                    "on_failure": True,
                    "email_recipients": []
                },
                "retry_settings": {
                    "max_retries": 3,
                    "retry_delay_minutes": 15
                }
            }
        
        current_app.logger.info("Retrieved PowerBI automation configuration from database")
        return config
    
    def update_automation_config(self, config):
        """
        Update PowerBI automation configuration in database
        
        Args:
            config (dict): New automation configuration
            
        Returns:
            bool: True if successful
        """
        current_app.logger.info("Updating PowerBI automation configuration")
        
        # Add metadata
        config["updated_at"] = datetime.utcnow()
        if "created_at" not in config:
            config["created_at"] = datetime.utcnow()
        
        # Update or insert configuration
        self.db.powerbi_automation_config.update_one(
            {},
            {"$set": config},
            upsert=True
        )
        
        current_app.logger.info("Successfully updated PowerBI automation configuration")
        return True
    
    def get_report_config(self, report_id):
        """
        Get configuration for a specific report
        
        Args:
            report_id (str): PowerBI report ID
            
        Returns:
            dict: Report configuration or None if not found
        """
        automation_config = self.get_automation_config()
        report_configs = automation_config.get("report_configs", [])
        
        for report_config in report_configs:
            if report_config.get("report_id") == report_id:
                return report_config
        
        return None
    
    def is_automation_enabled(self):
        """
        Check if automation is enabled
        
        Returns:
            bool: True if automation is enabled
        """
        config = self.get_automation_config()
        return config.get("enabled", False)
    
    def get_enabled_reports(self):
        """
        Get list of enabled report IDs
        
        Returns:
            list: List of enabled report IDs
        """
        config = self.get_automation_config()
        report_configs = config.get("report_configs", [])
        
        enabled_reports = []
        for report_config in report_configs:
            if report_config.get("enabled", True) and report_config.get("report_id"):
                enabled_reports.append(report_config["report_id"])
        
        return enabled_reports
    
    def log_automation_run(self, run_config, results, status="completed"):
        """
        Log an automation run to the database
        
        Args:
            run_config (dict): Configuration used for the run
            results (dict): Results of the automation run
            status (str): Status of the run (completed, failed, cancelled)
        """
        log_entry = {
            "timestamp": datetime.utcnow(),
            "status": status,
            "run_config": run_config,
            "results": results,
            "total_reports": results.get("total_reports", 0),
            "successful_reports": results.get("successful", 0),
            "failed_reports": results.get("failed", 0)
        }
        
        self.db.powerbi_automation_logs.insert_one(log_entry)
        current_app.logger.info(f"Logged automation run with status: {status}")
    
    def get_automation_history(self, limit=50, status_filter=None):
        """
        Get automation run history
        
        Args:
            limit (int): Maximum number of records to return
            status_filter (str): Optional status filter
            
        Returns:
            list: List of automation run logs
        """
        query = {}
        if status_filter:
            query["status"] = status_filter
        
        logs = list(
            self.db.powerbi_automation_logs
            .find(query)
            .sort("timestamp", -1)
            .limit(limit)
        )
        
        # Convert ObjectId to string for JSON serialization
        for log in logs:
            if "_id" in log:
                log["_id"] = str(log["_id"])
            if "timestamp" in log:
                log["timestamp"] = log["timestamp"].isoformat()
        
        return logs
    
    def get_automation_stats(self, days_back=30):
        """
        Get automation statistics for the last N days
        
        Args:
            days_back (int): Number of days to look back
            
        Returns:
            dict: Automation statistics
        """
        from datetime import timedelta
        
        start_date = datetime.utcnow() - timedelta(days=days_back)
        
        pipeline = [
            {"$match": {"timestamp": {"$gte": start_date}}},
            {
                "$group": {
                    "_id": "$status",
                    "count": {"$sum": 1},
                    "total_reports": {"$sum": "$total_reports"},
                    "successful_reports": {"$sum": "$successful_reports"},
                    "failed_reports": {"$sum": "$failed_reports"}
                }
            }
        ]
        
        stats_data = list(self.db.powerbi_automation_logs.aggregate(pipeline))
        
        # Format statistics
        stats = {
            "period_days": days_back,
            "total_runs": 0,
            "successful_runs": 0,
            "failed_runs": 0,
            "total_reports_processed": 0,
            "total_successful_reports": 0,
            "total_failed_reports": 0,
            "success_rate": 0.0
        }
        
        for stat in stats_data:
            status = stat["_id"]
            count = stat["count"]
            
            stats["total_runs"] += count
            stats["total_reports_processed"] += stat["total_reports"]
            stats["total_successful_reports"] += stat["successful_reports"]
            stats["total_failed_reports"] += stat["failed_reports"]
            
            if status == "completed":
                stats["successful_runs"] += count
            elif status == "failed":
                stats["failed_runs"] += count
        
        # Calculate success rate
        if stats["total_runs"] > 0:
            stats["success_rate"] = (stats["successful_runs"] / stats["total_runs"]) * 100
        
        return stats
    
    def cleanup_old_logs(self, days_to_keep=90):
        """
        Clean up old automation logs
        
        Args:
            days_to_keep (int): Number of days of logs to keep
            
        Returns:
            int: Number of logs deleted
        """
        from datetime import timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        result = self.db.powerbi_automation_logs.delete_many(
            {"timestamp": {"$lt": cutoff_date}}
        )
        
        deleted_count = result.deleted_count
        current_app.logger.info(f"Cleaned up {deleted_count} old automation logs")
        
        return deleted_count


powerbi_automation_config = PowerBIAutomationConfig()
