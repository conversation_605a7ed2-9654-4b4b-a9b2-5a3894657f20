from flask import current_app
from flaskr.models import get_db
from flaskr.services.powerbi_calculation_engine import powerbi_calculation_engine
from flaskr.services.powerbi_report_config import powerbi_report_config_service
from datetime import datetime, timedelta
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)


class PowerBIDataService:
    """
    Service for extracting and transforming data from MongoDB for PowerBI consumption
    """
    
    def __init__(self):
        self.db = get_db()
    
    def get_latest_banking_data(self, client_ids=None, days_back=30, limit=None):
        """
        Extract latest banking data from MongoDB
        
        Args:
            client_ids (list): Optional list of client IDs to filter
            days_back (int): Number of days to look back for data
            limit (int): Optional limit on number of records
            
        Returns:
            list: Transformed banking data ready for PowerBI
        """
        current_app.logger.info(f"Extracting banking data for last {days_back} days")
        
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days_back)
        
        # Build match criteria
        match_criteria = {
            "updated_at": {"$gte": start_date, "$lte": end_date}
        }
        
        if client_ids:
            match_criteria["client_id"] = {"$in": [ObjectId(cid) for cid in client_ids]}
        
        # Aggregation pipeline to get banking data with client info
        pipeline = [
            {"$match": match_criteria},
            {
                "$lookup": {
                    "from": "client_basic_info",
                    "localField": "client_id",
                    "foreignField": "_id",
                    "as": "client"
                }
            },
            {"$unwind": {"path": "$client", "preserveNullAndEmptyArrays": True}},
            {
                "$project": {
                    "client_id": {"$toString": "$client_id"},
                    "client_name": "$client.full_name",
                    "friendly_name": "$client.friendly_name",
                    "file_date": {"$arrayElemAt": ["$banking_files.file_date", -1]},
                    "file_name": {"$arrayElemAt": ["$banking_files.file_name", -1]},
                    "deposit_gbp": {"$arrayElemAt": ["$banking_files.deposit.GBP", -1]},
                    "deposit_eur": {"$arrayElemAt": ["$banking_files.deposit.EUR", -1]},
                    "deposit_usd": {"$arrayElemAt": ["$banking_files.deposit.USD", -1]},
                    "refund_gbp": {"$arrayElemAt": ["$banking_files.refund.GBP", -1]},
                    "refund_eur": {"$arrayElemAt": ["$banking_files.refund.EUR", -1]},
                    "refund_usd": {"$arrayElemAt": ["$banking_files.refund.USD", -1]},
                    "status": 1,
                    "updated_at": 1,
                    "created_at": 1
                }
            },
            {"$sort": {"updated_at": -1}}
        ]
        
        if limit:
            pipeline.append({"$limit": limit})
        
        banking_data = list(self.db.banking_metadata.aggregate(pipeline))
        
        # Transform for PowerBI
        transformed_data = []
        for record in banking_data:
            transformed_record = {
                "ClientID": record.get("client_id", ""),
                "ClientName": record.get("client_name", ""),
                "FriendlyName": record.get("friendly_name", ""),
                "FileDate": record.get("file_date", ""),
                "FileName": record.get("file_name", ""),
                "DepositGBP": float(record.get("deposit_gbp", 0) or 0),
                "DepositEUR": float(record.get("deposit_eur", 0) or 0),
                "DepositUSD": float(record.get("deposit_usd", 0) or 0),
                "RefundGBP": float(record.get("refund_gbp", 0) or 0),
                "RefundEUR": float(record.get("refund_eur", 0) or 0),
                "RefundUSD": float(record.get("refund_usd", 0) or 0),
                "Status": record.get("status", ""),
                "UpdatedAt": record.get("updated_at").isoformat() if record.get("updated_at") else "",
                "CreatedAt": record.get("created_at").isoformat() if record.get("created_at") else ""
            }
            transformed_data.append(transformed_record)
        
        current_app.logger.info(f"Extracted {len(transformed_data)} banking records")
        return transformed_data
    
    def get_latest_claims_data(self, client_ids=None, days_back=30, limit=None):
        """
        Extract latest claims data from MongoDB
        
        Args:
            client_ids (list): Optional list of client IDs to filter
            days_back (int): Number of days to look back for data
            limit (int): Optional limit on number of records
            
        Returns:
            list: Transformed claims data ready for PowerBI
        """
        current_app.logger.info(f"Extracting claims data for last {days_back} days")
        
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days_back)
        
        # Build match criteria
        match_criteria = {
            "updated_at": {"$gte": start_date, "$lte": end_date}
        }
        
        if client_ids:
            match_criteria["client_id"] = {"$in": [ObjectId(cid) for cid in client_ids]}
        
        # Aggregation pipeline to get claims data with client info
        pipeline = [
            {"$match": match_criteria},
            {
                "$lookup": {
                    "from": "client_basic_info",
                    "localField": "client_id",
                    "foreignField": "_id",
                    "as": "client"
                }
            },
            {"$unwind": {"path": "$client", "preserveNullAndEmptyArrays": True}},
            {
                "$project": {
                    "client_id": {"$toString": "$client_id"},
                    "client_name": "$client.full_name",
                    "friendly_name": "$client.friendly_name",
                    "file_date": {"$arrayElemAt": ["$claim_files.file_date", -1]},
                    "file_name": {"$arrayElemAt": ["$claim_files.file_name", -1]},
                    "claim_total_gbp": {"$arrayElemAt": ["$claim_files.claim_total.GBP", -1]},
                    "claim_total_eur": {"$arrayElemAt": ["$claim_files.claim_total.EUR", -1]},
                    "claim_total_usd": {"$arrayElemAt": ["$claim_files.claim_total.USD", -1]},
                    "item_count": {"$arrayElemAt": ["$claim_files.items", -1]},
                    "status": 1,
                    "frequency": 1,
                    "updated_at": 1,
                    "created_at": 1
                }
            },
            {"$sort": {"updated_at": -1}}
        ]
        
        if limit:
            pipeline.append({"$limit": limit})
        
        claims_data = list(self.db.claims_metadata.aggregate(pipeline))
        
        # Transform for PowerBI
        transformed_data = []
        for record in claims_data:
            transformed_record = {
                "ClientID": record.get("client_id", ""),
                "ClientName": record.get("client_name", ""),
                "FriendlyName": record.get("friendly_name", ""),
                "FileDate": record.get("file_date", ""),
                "FileName": record.get("file_name", ""),
                "ClaimTotalGBP": float(record.get("claim_total_gbp", 0) or 0),
                "ClaimTotalEUR": float(record.get("claim_total_eur", 0) or 0),
                "ClaimTotalUSD": float(record.get("claim_total_usd", 0) or 0),
                "ItemCount": record.get("item_count", {}),
                "Status": record.get("status", ""),
                "Frequency": record.get("frequency", ""),
                "UpdatedAt": record.get("updated_at").isoformat() if record.get("updated_at") else "",
                "CreatedAt": record.get("created_at").isoformat() if record.get("created_at") else ""
            }
            transformed_data.append(transformed_record)
        
        current_app.logger.info(f"Extracted {len(transformed_data)} claims records")
        return transformed_data
    
    def get_combined_summary_data(self, client_ids=None, days_back=30):
        """
        Get combined banking and claims summary data
        
        Args:
            client_ids (list): Optional list of client IDs to filter
            days_back (int): Number of days to look back for data
            
        Returns:
            list: Combined summary data for PowerBI
        """
        current_app.logger.info("Extracting combined banking and claims summary")
        
        banking_data = self.get_latest_banking_data(client_ids, days_back)
        claims_data = self.get_latest_claims_data(client_ids, days_back)
        
        # Create a summary by client
        client_summary = {}
        
        # Process banking data
        for record in banking_data:
            client_id = record["ClientID"]
            if client_id not in client_summary:
                client_summary[client_id] = {
                    "ClientID": client_id,
                    "ClientName": record["ClientName"],
                    "FriendlyName": record["FriendlyName"],
                    "TotalDepositsGBP": 0,
                    "TotalDepositsEUR": 0,
                    "TotalDepositsUSD": 0,
                    "TotalRefundsGBP": 0,
                    "TotalRefundsEUR": 0,
                    "TotalRefundsUSD": 0,
                    "TotalClaimsGBP": 0,
                    "TotalClaimsEUR": 0,
                    "TotalClaimsUSD": 0,
                    "LastBankingUpdate": record["UpdatedAt"],
                    "LastClaimsUpdate": ""
                }
            
            client_summary[client_id]["TotalDepositsGBP"] += record["DepositGBP"]
            client_summary[client_id]["TotalDepositsEUR"] += record["DepositEUR"]
            client_summary[client_id]["TotalDepositsUSD"] += record["DepositUSD"]
            client_summary[client_id]["TotalRefundsGBP"] += record["RefundGBP"]
            client_summary[client_id]["TotalRefundsEUR"] += record["RefundEUR"]
            client_summary[client_id]["TotalRefundsUSD"] += record["RefundUSD"]
        
        # Process claims data
        for record in claims_data:
            client_id = record["ClientID"]
            if client_id not in client_summary:
                client_summary[client_id] = {
                    "ClientID": client_id,
                    "ClientName": record["ClientName"],
                    "FriendlyName": record["FriendlyName"],
                    "TotalDepositsGBP": 0,
                    "TotalDepositsEUR": 0,
                    "TotalDepositsUSD": 0,
                    "TotalRefundsGBP": 0,
                    "TotalRefundsEUR": 0,
                    "TotalRefundsUSD": 0,
                    "TotalClaimsGBP": 0,
                    "TotalClaimsEUR": 0,
                    "TotalClaimsUSD": 0,
                    "LastBankingUpdate": "",
                    "LastClaimsUpdate": record["UpdatedAt"]
                }
            
            client_summary[client_id]["TotalClaimsGBP"] += record["ClaimTotalGBP"]
            client_summary[client_id]["TotalClaimsEUR"] += record["ClaimTotalEUR"]
            client_summary[client_id]["TotalClaimsUSD"] += record["ClaimTotalUSD"]
            client_summary[client_id]["LastClaimsUpdate"] = record["UpdatedAt"]
        
        # Calculate balances
        summary_data = []
        for client_data in client_summary.values():
            client_data["BalanceGBP"] = (client_data["TotalDepositsGBP"] - 
                                       client_data["TotalRefundsGBP"] - 
                                       client_data["TotalClaimsGBP"])
            client_data["BalanceEUR"] = (client_data["TotalDepositsEUR"] - 
                                       client_data["TotalRefundsEUR"] - 
                                       client_data["TotalClaimsEUR"])
            client_data["BalanceUSD"] = (client_data["TotalDepositsUSD"] - 
                                       client_data["TotalRefundsUSD"] - 
                                       client_data["TotalClaimsUSD"])
            summary_data.append(client_data)
        
        current_app.logger.info(f"Generated summary for {len(summary_data)} clients")
        return summary_data

    def get_calculated_report_data(self, report_id: str, client_ids=None, days_back=30):
        """
        Get calculated data for a specific PowerBI report using its configuration

        Args:
            report_id (str): PowerBI report ID
            client_ids (list): Optional list of client IDs to filter
            days_back (int): Number of days to look back for data

        Returns:
            list: Calculated data ready for PowerBI based on report configuration
        """
        current_app.logger.info(f"Generating calculated data for report: {report_id}")

        # Get report configuration
        report_config = powerbi_report_config_service.get_report_config(report_id)

        # Get raw data
        banking_data = self.get_latest_banking_data(client_ids, days_back)
        claims_data = self.get_latest_claims_data(client_ids, days_back)

        # Apply calculations using the engine
        calculated_data = powerbi_calculation_engine.calculate_report_data(
            report_config, banking_data, claims_data
        )

        # Filter to only include configured output columns
        output_columns = report_config.get('output_columns', [])
        if output_columns:
            filtered_data = []
            for record in calculated_data:
                filtered_record = {col: record.get(col) for col in output_columns if col in record}
                filtered_data.append(filtered_record)
            calculated_data = filtered_data

        current_app.logger.info(f"Generated {len(calculated_data)} calculated records for report {report_id}")
        return calculated_data

    def preview_calculated_data(self, report_id: str, client_ids=None, days_back=7, limit=10):
        """
        Preview calculated data for testing purposes

        Args:
            report_id (str): PowerBI report ID
            client_ids (list): Optional list of client IDs to filter
            days_back (int): Number of days to look back for data
            limit (int): Maximum number of records to return

        Returns:
            dict: Preview data with metadata
        """
        current_app.logger.info(f"Generating preview for report: {report_id}")

        # Get report configuration
        report_config = powerbi_report_config_service.get_report_config(report_id)

        # Get limited raw data
        banking_data = self.get_latest_banking_data(client_ids, days_back, limit)
        claims_data = self.get_latest_claims_data(client_ids, days_back, limit)

        # Apply calculations
        calculated_data = powerbi_calculation_engine.calculate_report_data(
            report_config, banking_data, claims_data
        )

        # Limit results
        if len(calculated_data) > limit:
            calculated_data = calculated_data[:limit]

        return {
            "report_id": report_id,
            "report_name": report_config.get("name", "Unknown"),
            "record_count": len(calculated_data),
            "banking_records": len(banking_data),
            "claims_records": len(claims_data),
            "calculation_rules_count": len(report_config.get("calculation_rules", [])),
            "output_columns": report_config.get("output_columns", []),
            "preview_data": calculated_data,
            "configuration": {
                "filters": report_config.get("filters", []),
                "grouping": report_config.get("grouping", {}),
                "calculation_rules": report_config.get("calculation_rules", [])
            }
        }

    def validate_report_data(self, report_id: str, calculated_data: list) -> dict:
        """
        Validate calculated data before sending to PowerBI

        Args:
            report_id (str): PowerBI report ID
            calculated_data (list): Calculated data to validate

        Returns:
            dict: Validation results
        """
        current_app.logger.info(f"Validating data for report: {report_id}")

        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "record_count": len(calculated_data),
            "null_counts": {},
            "data_types": {},
            "value_ranges": {}
        }

        if not calculated_data:
            validation_results["errors"].append("No data to validate")
            validation_results["is_valid"] = False
            return validation_results

        # Get expected columns from report config
        report_config = powerbi_report_config_service.get_report_config(report_id)
        expected_columns = report_config.get("output_columns", [])

        # Check for missing columns
        first_record = calculated_data[0]
        actual_columns = set(first_record.keys())
        expected_columns_set = set(expected_columns)

        missing_columns = expected_columns_set - actual_columns
        extra_columns = actual_columns - expected_columns_set

        if missing_columns:
            validation_results["errors"].append(f"Missing columns: {list(missing_columns)}")
            validation_results["is_valid"] = False

        if extra_columns:
            validation_results["warnings"].append(f"Extra columns: {list(extra_columns)}")

        # Analyze data quality
        for column in actual_columns:
            values = [record.get(column) for record in calculated_data]
            null_count = sum(1 for v in values if v is None or v == "")

            validation_results["null_counts"][column] = null_count

            # Check data types
            non_null_values = [v for v in values if v is not None and v != ""]
            if non_null_values:
                data_type = type(non_null_values[0]).__name__
                validation_results["data_types"][column] = data_type

                # Check value ranges for numeric columns
                if data_type in ['int', 'float']:
                    validation_results["value_ranges"][column] = {
                        "min": min(non_null_values),
                        "max": max(non_null_values),
                        "avg": sum(non_null_values) / len(non_null_values)
                    }

            # Warn about high null percentages
            null_percentage = (null_count / len(calculated_data)) * 100
            if null_percentage > 50:
                validation_results["warnings"].append(
                    f"Column '{column}' has {null_percentage:.1f}% null values"
                )

        current_app.logger.info(f"Validation completed for report {report_id}: {validation_results['is_valid']}")
        return validation_results


powerbi_data_service = PowerBIDataService()
