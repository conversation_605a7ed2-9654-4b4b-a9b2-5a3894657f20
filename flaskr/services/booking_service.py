from datetime import datetime
from functools import reduce

from flask import current_app

from flaskr.helpers.date_util import date_format
from flaskr.models import get_db
from flaskr.models.booking.booking_claim_checks import BookingClaimChecksSchema
from flaskr.models.banking.banking_file_details import BankingFileDetailsSchema
from bson import ObjectId
from pymongo import ReadPreference

from flaskr.models.claims.claims_file_details import ClaimsFileDetailsSchema
from flaskr.models.booking.trust_fund import TrustFundSchema
from flaskr.services.exceptions import ServiceException
from flaskr.helpers import round
from flaskr.services.auth_service import auth_service
import os


class BookingService:
    def booking_claim_checks(self, client_id, claim_transaction_id):
        with get_db().client.start_session() as session:
            with session.start_transaction(read_preference=ReadPreference.PRIMARY):
                booking_claim_checks = list(
                    get_db().booking_claim_checks.find(
                        {"client_id": ObjectId(client_id), "transaction_id": ObjectId(claim_transaction_id)}
                    )
                )
                trust_type_name = None
                client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
                if client_basic_info["type_of_trust_account"] is not None:
                    trust_type = get_db().lookup_trust_type.find_one(
                        {"_id": client_basic_info["type_of_trust_account"]}
                    )
                    trust_type_name = trust_type["name"]
                if not booking_claim_checks:
                    lookup_default_checks = get_db().lookup_default_checks.find(
                        {"applicable_trust": {"$in": ["All", trust_type_name]}}
                    )
                    default_checks = [
                        {
                            "name": check["name"],
                            "description": check["description"],
                            "notes": "",
                            "selected": False,
                            "client_id": ObjectId(client_id),
                            "transaction_id": ObjectId(claim_transaction_id),
                        }
                        for check in lookup_default_checks
                    ]
                    client_additional_checks = get_db().client_check_info.find({"client_id": ObjectId(client_id)})
                    additional_checks = [
                        {
                            "name": check["check_name"],
                            "description": check["description"],
                            "notes": "",
                            "selected": False,
                            "client_id": ObjectId(client_id),
                            "transaction_id": ObjectId(claim_transaction_id),
                        }
                        for check in client_additional_checks
                    ]
                    booking_claim_checks = [*default_checks, *additional_checks]
                    get_db().booking_claim_checks.insert_many(booking_claim_checks, session=session)

                booking_claim_checks = list(
                    get_db().booking_claim_checks.find(
                        {"client_id": ObjectId(client_id), "transaction_id": ObjectId(claim_transaction_id)},
                        session=session,
                    )
                )

                ptt_users = auth_service.ptt_users_list()
                response = []
                for item in booking_claim_checks:
                    user = next(filter(lambda x: x["userId"] == item.get("modified_by"), ptt_users), {})
                    item["modified_by"] = user.get("name") or user.get("email")
                    item["client_id"] = client_basic_info["c_id"]
                    response.append(BookingClaimChecksSchema().dump(item))

                return response

    def list_payments(self, client_id, booking_ref, query):
        payments = list(
            get_db().banking_file_details.find(
                {
                    "client_id": ObjectId(client_id),
                    "booking_ref": booking_ref,
                    "deleted": False,
                    "$or": [
                        {"currency_code": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                        {"customer_type": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                    ],
                },
                projection={
                    "booking_date": 1,
                    "currency_code": 1,
                    "amount": 1,
                    "departure_date": 1,
                    "return_date": 1,
                    "customer_type": 1,
                    "banking_id": 1,
                    "payment_date": 1,
                    "type": 1,
                    "payment_type": 1,
                    "element": 1,
                },
                collation={"locale": "en", "strength": 1},
            )
        )
        payment_list = [BankingFileDetailsSchema().dump(payment) for payment in payments]
        total_amount = reduce(
            lambda x, y: x + y["amount"],
            payment_list,
            0,
        )
        refunds = reduce(
            lambda x, y: x + (y["amount"] if y["amount"] < 0 else 0),
            payment_list,
            0,
        )
        deposits = total_amount - refunds
        count = len(payment_list)

        return {
            "total": total_amount,
            "refunds": refunds,
            "deposits": deposits,
            "count": count,
            "payments": payment_list,
        }

    def booking_claims(self, client_id, booking_ref, query):
        claims = list(
            get_db().claims_file_details.find(
                {
                    "client_id": ObjectId(client_id),
                    "booking_ref": booking_ref,
                    "deleted": False,
                    "$or": [{"currency_code": {"$regex": f"^.*{query}.*$", "$options": "i"}}],
                },
                projection={
                    "booking_date": 1,
                    "amount": 1,
                    "currency_code": 1,
                    "claims_id": 1,
                    "element": 1,
                    "supplier_ref": 1,
                    "supplier_names": 1,
                    "total_due_to_supplier": 1,
                    "type": 1,
                },
                collation={"locale": "en", "strength": 1},
            )
        )

        claims_list = []
        for transaction in claims:
            claims_metadata = get_db().claims_metadata.find_one({"_id": transaction["claims_id"]})
            file_date = claims_metadata["claim_files"][-1]["file_date"]
            claims_list.append({**ClaimsFileDetailsSchema().dump(transaction), "claimDate": file_date})

        count = len(claims_list)

        return {"count": count, "claims": claims_list}

    def booking_search(self, client_id, booking_ref):
        client = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not client:
            raise ServiceException("Client not found")
        
        collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
        booking = get_db()[collection_name].find_one(
            {"client_id": ObjectId(client_id), "booking_ref": booking_ref},
            projection={
                "balance": 1,
                "total_in_trust": 1,
                "total_claimed": 1,
                "lead_pax": 1,
                "booking_date": 1,
                "departure_date": 1,
                "return_date": 1,
                "pax_count": 1,
                "bonding": 1,
                "total_booking_value": 1,
                "created_at": 1,
                "updated_at": 1,
                "total_due_to_supplier": 1,
                "total_paid_by_customer": 1,
                "date_customer_paid": 1,
                "supplier": 1,
            },
            collation={"locale": "en", "strength": 1},
        )

        if not booking:
            raise ServiceException("Booking info not found")

        booking["nights"] = (
            (
                datetime.strptime(booking["return_date"], "%Y-%m-%d")
                - (datetime.strptime(booking["departure_date"], "%Y-%m-%d"))
            ).days
            if booking.get("return_date") and booking.get("departure_date")
            else None
        )
        booking["total_booking_value"] = booking["total_booking_value"] if booking.get("total_booking_value") else 0

        booking["balance"] = round(booking["balance"], 2) if booking.get("balance") else 0
        if booking["balance"] == -0.0:
            booking["balance"] = abs(booking["balance"])

        booking["total_in_trust"] = booking["total_in_trust"] if booking.get("total_in_trust") else 0

        booking["total_claimed"] = booking.get("total_claimed") if booking.get("total_claimed") else 0

        return {
            **TrustFundSchema().dump(booking),
            "clientName": client["full_name"],
            "friendlyName": client["friendly_name"],
        }

    def update_booking_claim_checks(self, checks_list, user_id):
        with get_db().client.start_session() as session:
            with session.start_transaction():
                for check in checks_list:
                    existing_check = get_db().booking_claim_checks.find_one(
                        {"_id": ObjectId(check["_id"])}, projection={"_id": 0, "notes": 1, "selected": 1}
                    )
                    if not {"notes": check["notes"], "selected": check["selected"]} == existing_check:
                        get_db().booking_claim_checks.update_one(
                            {"_id": ObjectId(check["_id"])},
                            {
                                "$set": {
                                    "notes": check["notes"],
                                    "selected": check["selected"],
                                    "modified_by": user_id,
                                    "updated_at": datetime.utcnow(),
                                }
                            },
                            session=session,
                        )

    def list_anomalies(self, client_id, booking_ref, query):

        banking_anomalies = list(
            get_db().anomaly_banking.aggregate(
                [
                    {
                        "$addFields": {
                            "anomaly_category": "banking",
                        }
                    },
                    {
                        "$match": {
                            "$and": [
                                {"deleted": False},
                                {"client_id": ObjectId(client_id)},
                                {"booking_ref": booking_ref},
                                {
                                    "$or": [
                                        {"status": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"anomaly_type": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"anomaly_category": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    ]
                                },
                            ]
                        }
                    },
                    {
                        "$project": {
                            "anomaly_type": "$anomaly_type",
                            "status": "$status",
                            "anomaly_category": "$anomaly_category",
                        }
                    },
                    {
                        "$group": {
                            "_id": "$anomaly_type",
                            "count": {"$sum": 1},
                            "anomalies": {"$push": "$$ROOT"},
                        },
                    },
                    {
                        "$addFields": {
                            "anomaly_category": "banking",
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
            )
        )
        claim_anomalies = list(
            get_db().anomaly_claims.aggregate(
                [
                    {
                        "$addFields": {
                            "anomaly_category": "claims",
                        }
                    },
                    {
                        "$match": {
                            "$and": [
                                {"deleted": False},
                                {"client_id": ObjectId(client_id)},
                                {"booking_ref": booking_ref},
                                {
                                    "$or": [
                                        {"status": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"anomaly_type": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                        {"anomaly_category": {"$regex": f"^.*{query}.*$", "$options": "i"}},
                                    ]
                                },
                            ]
                        }
                    },
                    {
                        "$project": {
                            "anomaly_type": "$anomaly_type",
                            "status": "$status",
                            "anomaly_category": "$anomaly_category",
                        }
                    },
                    {
                        "$group": {
                            "_id": "$anomaly_type",
                            "count": {"$sum": 1},
                            "anomalies": {"$push": "$$ROOT"},
                        },
                    },
                    {
                        "$addFields": {
                            "anomaly_category": "claims",
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
            )
        )
        anomalies = [*banking_anomalies, *claim_anomalies]
        content = []
        for row in anomalies:
            status = "Resolved"
            for item in row["anomalies"]:
                if item["status"] == "Unresolved":
                    status = "Unresolved"
                    break
            anomalies_dict = {
                "count": row["count"],
                "anomalyType": row["_id"],
                "status": status,
                "anomalyCategory": row["anomaly_category"],
            }
            content.append(anomalies_dict)
        response = {"anomalies": content}
        return response

    def booking_update(self, data):
        try:
            if data.get("departure_date"):
                data["departure_date"] = date_format(data["departure_date"])
            if data.get("return_date"):
                data["return_date"] = date_format(data["return_date"])
            if data.get("booking_date"):
                data["booking_date"] = date_format(data["booking_date"])
            if data.get("date_customer_paid"):
                data["date_customer_paid"] = date_format(data["date_customer_paid"])

        except ValueError:
            raise ServiceException("Invalid Date Formats")
        
        collection_name = "trust_fund" if str(data["client_id"]) == os.environ.get("NAS") else "trust_fund_v2"
        print(f"collection_name is ${collection_name}")
        existing_booking = get_db()[collection_name].find_one(
            {"client_id": ObjectId(data["client_id"]), "booking_ref": data["booking_ref"]},
            collation={"locale": "en", "strength": 1},
        )
        print(f"existing_booking is ${existing_booking}")
        if not existing_booking:
            raise ServiceException("booking not found")
        if (
            data.get("booking_date")
            and existing_booking.get("booking_date")
            and data.get("client_id")
            in [
                current_app.config["MAJOR_TRAVEL"],
                current_app.config["WST_TRAVEL"],
                current_app.config["ANGLIA_TOURS"],
            ]
            and (
                (
                    datetime.strptime(existing_booking.get("booking_date"), "%Y-%m-%d")
                    < datetime.strptime("2022-04-01", "%Y-%m-%d")
                    <= datetime.strptime(data.get("booking_date"), "%Y-%m-%d")
                )
                or (
                    datetime.strptime(existing_booking.get("booking_date"), "%Y-%m-%d")
                    >= datetime.strptime("2022-04-01", "%Y-%m-%d")
                    > datetime.strptime(data.get("booking_date"), "%Y-%m-%d")
                )
            )
        ):
            raise ServiceException("Booking is not editable for the given booking date")
        get_db()[collection_name].update_one(
            {"client_id": ObjectId(data["client_id"]), "booking_ref": data["booking_ref"]},
            {
                "$set": {
                    **data,
                    "client_id": ObjectId(data["client_id"]),
                    "updated_at": datetime.utcnow(),
                }
            },
            collation={"locale": "en", "strength": 1},
        )


booking_service = BookingService()
