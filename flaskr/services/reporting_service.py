from collections import OrderedDict
import csv
import logging
import math
import re
import string
from collections.abc import Generator
from flask import current_app, jsonify, make_response, abort
import xlsxwriter
import time
from math import ceil
from flaskr.helpers.boto3_handler import download_file, upload_file

import os
from flaskr.models import get_db
from flaskr.models.client.atol_info import ClientAtolInfoSchema
from flaskr.models.report_files import ReportFilesSchema
from datetime import datetime, timedelta
from flaskr.helpers.date_util import (
    change_month_format,
    format_date_with_day_ordinal,
    change_date_format,
    format_date_from_numeric,
)
from flaskr.helpers.reporting_util import extract_amount
from flaskr.helpers.currency_util import get_currency_symbol
from flaskr.services.exceptions import ServiceException
from bson import ObjectId
from flaskr.helpers import round
import requests

# Directory to save temporary files
TEMP_DIR = "/tmp"
if not os.path.exists(TEMP_DIR):
    os.makedirs(TEMP_DIR)


class ReportingService:
    def __transform_tbr_content(self, data: Generator, client: str) -> Generator:
        if client == os.environ.get("NAS"):
            for row in data:
                currency_list = row.get("currency", [])
                # For NAS, take the first non-None currency if available
                if isinstance(currency_list, list) and currency_list:
                    currency_value = next((cur for cur in currency_list if cur is not None), "")
                else:
                    currency_value = ""
                yield {
                    **row,
                    "currency": currency_value,
                    "_id": str(row["_id"]),
                    "clientId": str(row["clientId"]),
                }
        else:
            for row in data:
                yield {
                    **row,
                    "currency": ", ".join(cur for cur in row["currency"] if cur is not None),
                    "_id": str(row["_id"]),
                    "clientId": str(row["clientId"]),
                }

    def __transform_tbr_content_nas(self, trust_funds: Generator) -> Generator:
        for trust_fund in trust_funds:
            payment_list = trust_fund["banking_amount"].copy()
            claim_list = trust_fund["claim_amount"]
            positive_claim_total = 0
            negative_claim_total = 0
            for amount in claim_list:
                if amount > 0:
                    positive_claim_total += amount
                else:
                    negative_claim_total += amount
            for i, payment in enumerate(payment_list):
                if payment_list[i] > 0 and positive_claim_total > 0:
                    payment_list[i] = max(payment - positive_claim_total, 0)
                    positive_claim_total = max(positive_claim_total - payment, 0)
                elif payment_list[i] < 0 and negative_claim_total < 0:
                    payment_list[i] = min(payment - negative_claim_total, 0)
                    negative_claim_total = min(negative_claim_total - payment, 0)
            for i in range(len(trust_fund["banking_amount"])):
                if round(payment_list[i]) == 0:
                    continue
                item = trust_fund.copy()
                del item["banking_amount"]
                del item["claim_amount"]
                del item["payment_id"]
                item["status"] = item["bookingStatus"] if item.get("bookingStatus") else "Active"
                item["totalBookingValue"] = item.get("totalBookingValue")
                item["deposits"] = trust_fund["banking_amount"][i] if trust_fund["banking_amount"][i] > 0 else 0.0
                item["refundsFromDepositFile"] = (
                    abs(trust_fund["banking_amount"][i]) if trust_fund["banking_amount"][i] < 0 else 0.0
                )
                item["totalBanked"] = item["deposits"] - item["refundsFromDepositFile"]
                item["totalClaimed"] = trust_fund["banking_amount"][i] - payment_list[i]
                item["balance"] = payment_list[i]
                item["_id"] = trust_fund["payment_id"][i]
                item["supplierName"] = item["supplierNames"][i] if item["supplierNames"][i] is not None else None
                item["type"] = item["types"][i] if item["types"][i] is not None else None
                yield item

    def __summary_for_status(self, client_id, currency, from_date, to_date, status):
        banking_amount = get_db().banking_metadata.aggregate(
            [
                {"$match": {"client_id": ObjectId(client_id), "status": status}},
                {"$project": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                {
                    "$project": {
                        "file_date": "$recent_file.file_date",
                        "amount": {"$objectToArray": "$recent_file.deposit"},
                    }
                },
                {"$unwind": "$amount"},
                {"$match": {"file_date": {"$gte": from_date, "$lte": to_date}, "amount.k": currency}},
                {"$group": {"_id": None, "amount": {"$sum": "$amount.v"}}},
            ]
        )
        banking_amount = next(banking_amount, {"amount": 0})["amount"]

        claim_amount = get_db().claims_metadata.aggregate(
            [
                {"$match": {"client_id": ObjectId(client_id), "status": status}},
                {"$project": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
                {
                    "$project": {
                        "file_date": "$recent_file.file_date",
                        "amount": {"$objectToArray": "$recent_file.claim_amount"},
                    }
                },
                {"$unwind": "$amount"},
                {"$match": {"file_date": {"$gte": from_date, "$lte": to_date}, "amount.k": currency}},
                {"$group": {"_id": None, "amount": {"$sum": "$amount.v"}}},
            ]
        )
        claim_amount = next(claim_amount, {"amount": 0})["amount"]

        return {"banking_amount": banking_amount, "claim_amount": claim_amount}

    def __movements_report_data(self, client_id, from_date, to_date, period=""):
        client_match_condition = [{"client_id": client_id}] if client_id else []
        banking_data = list(
            get_db().banking_metadata.aggregate(
                [
                    {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                    {
                        "$addFields": {
                            "file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}},
                            "amount": {"$objectToArray": "$recent_file.deposit"},
                        }
                    },
                    {"$unwind": "$amount"},
                    {
                        "$match": {
                            "$and": [
                                *client_match_condition,
                                {"file_datetime": {"$gte": from_date, "$lte": to_date}},
                                {"status": {"$nin": ["Cancelled", "Cancelled by System"]}},
                            ]
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {"$unwind": "$client"},
                    {
                        "$group": {
                            "_id": {"client_id": "$client_id", "currency_code": "$amount.k"},
                            "amount": {"$sum": "$amount.v"},
                            "client_name": {"$first": "$client.full_name"},
                            "friendly_name": {"$first": "$client.friendly_name"},
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "clientId": "$_id.client_id",
                            "friendlyName": "$friendly_name",
                            "clientName": "$client_name",
                            "currencyCode": "$_id.currency_code",
                            "amount": "$amount",
                        }
                    },
                ]
            )
        )
        for item in banking_data:
            item[f"receipts{period}"] = item.pop("amount")
            item[f"receipts{period}Thousandth"] = item[f"receipts{period}"] / 1000

        claims_data = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$addFields": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
                    {
                        "$addFields": {
                            "file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}},
                            "amount": {"$objectToArray": "$recent_file.claim_total"},
                        }
                    },
                    {"$unwind": "$amount"},
                    {
                        "$match": {
                            "$and": [
                                *client_match_condition,
                                {"file_datetime": {"$gte": from_date, "$lte": to_date}},
                                {"status": {"$nin": ["Cancelled", "Cancelled by System"]}},
                            ]
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {"$unwind": "$client"},
                    {
                        "$group": {
                            "_id": {"client_id": "$client_id", "currency_code": "$amount.k"},
                            "amount": {"$sum": "$amount.v"},
                            "client_name": {"$first": "$client.full_name"},
                            "friendly_name": {"$first": "$client.friendly_name"},
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "clientId": "$_id.client_id",
                            "friendlyName": "$friendly_name",
                            "clientName": "$client_name",
                            "currencyCode": "$_id.currency_code",
                            "amount": "$amount",
                        }
                    },
                ]
            )
        )
        for item in claims_data:
            item[f"payments{period}"] = item.pop("amount")
            item[f"payments{period}Thousandth"] = item[f"payments{period}"] / 1000

        response = (banking_data, claims_data)
        return response

    def _generate_header_dict(self, trust_type):
        header_dict = OrderedDict(
            [
                ("cId", "Client ID"),
                ("clientName", trust_type),
                ("friendlyName", "Friendly Name"),
                ("claiminday", "Claim in day"),
                ("claimindate", "Claim in date"),
                ("claimintime", "Claim in time"),
                ("frequency", "Frequency"),
                ("element", "Elements"),
                ("currency", "Currency"),
                ("amount", "Amount"),
                ("status", "Status"),
                ("notes", "Notes"),
            ]
        )
        header_dict["clientName"] = trust_type
        return header_dict

    def _write_data_to_worksheet(self, row, col, workbook, worksheet, header_dict, data_list):
        amount_list = [
            "amounts",
            "amount",
            "minAmount",
            "maxAmount",
            "total",
            "deposits",
            "totalBanked",
            "totalClaimed",
            "balance",
            "validClaimAmount",
            "voidClaimAmount",
            "claimFileTotal",
            "bankingFileTotal",
            "value",
            "refundsFromDepositFile",
            "totalAmount",
            "totalMaxCap",
            "totalAgentAmountInTrust",
            "totalInTrust",
            "totalBookingValue",
            "totalClaimAmount",
            "deposits",
            "originalClaim",
            "revisedClaim",
            "balanceAT",
            "actualAT",
        ]
        date_list = ["fileDate", "fileSubmitted", "goLiveDate", "fromDate", "toDate"]
        align_left_list = ["element"]

        head_dict = {}
        merge_left = workbook.add_format({"size": 11, "align": "left"})
        align_center = workbook.add_format({"size": 11, "align": "center", "valign": "vcenter"})
        merge_left_head = workbook.add_format(
            {"bold": 1, "size": 11, "align": "center", "valign": "vcenter", "border": 1}
        )
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right"})
        date_format = workbook.add_format({"num_format": "dd-mm-yyyy", "align": "center", "valign": "vcenter"})

        for key, value in header_dict.items():
            worksheet.write(row, col, value, merge_left_head)
            if key in amount_list:
                worksheet.set_column(col, col, 20, amount_format)
            elif key in date_list:
                worksheet.set_column(col, col, 20, date_format)
            elif key in align_left_list:
                worksheet.set_column(col, col, None, merge_left)
            else:
                worksheet.set_column(col, col, 20, align_center)

            head_dict[key] = col
            col += 1

        row += 1
        for data in data_list:
            for key in header_dict.keys():
                value = data.get(key)
                worksheet.write(row, head_dict[key], value)
                if key in data_list:
                    value = datetime.strptime(value, "%d-%m-%Y")
                    worksheet.write(row, head_dict[key], value)
            row += 1
        return worksheet, row, col

    def generate_excel_report_claim_search_summary(
        self, atol_standard_data_list, non_travel_clients_data_list, other_accounts_data_list, name
    ):
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()
        row = 0
        col = 0

        header_dict_escrow = self._generate_header_dict("Escrow Clients")
        header_dict_other = self._generate_header_dict("Other Clients")
        header_dict_non_travel = self._generate_header_dict("Non-Travel Clients")

        worksheet, row, col = self._write_data_to_worksheet(
            row, col, workbook, worksheet, header_dict_escrow, atol_standard_data_list
        )
        col = 0
        row += 3
        worksheet, row, col = self._write_data_to_worksheet(
            row, col, workbook, worksheet, header_dict_other, other_accounts_data_list
        )
        col = 0
        row += 3
        worksheet, row, col = self._write_data_to_worksheet(
            row, col, workbook, worksheet, header_dict_non_travel, non_travel_clients_data_list
        )
        workbook.close()

    def generate_excel_report(self, header_dict, data_list, name):
        amount_list = [
            "amounts",
            "amount",
            "minAmount",
            "maxAmount",
            "total",
            "deposits",
            "totalBanked",
            "totalClaimed",
            "balance",
            "validClaimAmount",
            "voidClaimAmount",
            "claimFileTotal",
            "bankingFileTotal",
            "value",
            "refundsFromDepositFile",
            "totalAmount",
            "totalMaxCap",
            "totalAgentAmountInTrust",
            "totalInTrust",
            "totalBookingValue",
            "totalClaimAmount",
            "deposits",
            "originalClaim",
            "revisedClaim",
            "balanceAT",
            "actualAT",
            "amountRefunded",
            "convertedAmount",
            "fee",
        ]
        date_list = ["fileDate", "fileSubmitted", "goLiveDate", "fromDate", "toDate"]
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()
        merge_left = workbook.add_format({"size": 11, "align": "left"})
        merge_left_head = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right"})
        date_format = workbook.add_format({"num_format": "dd-mm-yyyy", "align": "left"})

        row = 0
        col = 0
        head_dict = {}

        for key, value in header_dict.items():
            worksheet.write(row, col, value, merge_left_head)
            if key in amount_list:
                worksheet.set_column(col, col, 20, amount_format)
            elif key in date_list:
                worksheet.set_column(col, col, 20, date_format)
            else:
                worksheet.set_column(col, col, 20, merge_left)

            head_dict[key] = col
            col += 1
        row = 1

        for data in data_list:
            for key in header_dict.keys():
                value = data.get(key)
                worksheet.write(row, head_dict[key], value)
                if key in data_list:
                    value = datetime.strptime(value, "%d-%m-%Y")
                    worksheet.write(row, head_dict[key], value)
            row += 1
        workbook.close()

    def generate_csv_report(self, header_dict, data_list, name):
        amount_list = [
            "amounts",
            "amount",
            "minAmount",
            "maxAmount",
            "total",
            "deposits",
            "totalBanked",
            "totalClaimed",
            "balance",
            "validClaimAmount",
            "voidClaimAmount",
            "claimFileTotal",
            "bankingFileTotal",
            "value",
            "refundsFromDepositFile",
            "totalAmount",
            "totalAgentAmountInTrust",
            "totalInTrust",
            "totalBookingValue",
            "totalClaimAmount",
            "deposits",
            "revisedClaim",
        ]
        csv_file = open(f"{current_app.config['TEMP_DIR']}/{name}", "w")
        writer = csv.writer(csv_file, delimiter=",")
        writer.writerow(header_dict.values())

        for data in data_list:
            value_list = []
            for key in header_dict.keys():
                value = data.get(key)
                if key in amount_list and value:
                    value = round(value, 2)
                value_list.append(value)
            writer.writerow(value_list)

        csv_file.close()

    def generate_csv_and_excel_report(self, header_dict, data_list, csv_name, xlsx_name):
        logging.info(f"CSV File Path: {csv_name}")
        logging.info(f"XLSX File Path: {xlsx_name}")
        logging.info(f"Data list: {data_list}")
        amount_list = [
            "amounts",
            "amount",
            "minAmount",
            "maxAmount",
            "total",
            "deposits",
            "totalBanked",
            "totalClaimed",
            "balance",
            "validClaimAmount",
            "voidClaimAmount",
            "claimFileTotal",
            "bankingFileTotal",
            "value",
            "refundsFromDepositFile",
            "totalAmount",
            "totalAgentAmountInTrust",
            "totalInTrust",
            "totalBookingValue",
            "totalClaimAmount",
            "deposits",
            "revisedClaim",
        ]
        csv_file = open(f"{current_app.config['TEMP_DIR']}/{csv_name}", "w")
        writer = csv.writer(csv_file, delimiter=",")
        writer.writerow(header_dict.values())

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{xlsx_name}")
        worksheet = workbook.add_worksheet()
        merge_left = workbook.add_format({"size": 11, "align": "left"})
        merge_left_head = workbook.add_format({"bold": 1, "size": 11, "align": "left"})

        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right"})

        row = 0
        col = 0
        head_dict = {}
        logging.info("Writing headers to Excel and setting column formats...")
        for key, value in header_dict.items():
            worksheet.write(row, col, value, merge_left_head)
            if key in amount_list:
                worksheet.set_column(col, col, 20, amount_format)
            else:
                worksheet.set_column(col, col, 20, merge_left)

            head_dict[key] = col
            col += 1
        row = 1

        logging.info("Processing records")
        for i, data in enumerate(data_list):
            try:
                value_list = []
                for key in header_dict.keys():
                    try:
                        value = data.get(key)
                        worksheet.write(row, head_dict[key], value)
                        if key in amount_list and value:
                            value = round(value, 2)
                        # logging.info(f'Processed value for key "{key}": {value}')
                        value_list.append(value)
                    except Exception as inner_e:
                        logging.error(f"Error processing key '{key}' in record {i}: {inner_e}")
                row += 1
                writer.writerow(value_list)
            except Exception as outer_e:
                logging.error(f"Error processing record {i}: {outer_e}")

        logging.info("Closing workbook and CSV file")
        csv_file.close()
        workbook.close()

    def trust_balance_report_gtl(self, data):

        currency_projection_criteria = {"currency": "$currency_code"}
        client = data.get("client")
        currency = data.get("currency") or ""
        page = int(data.get("page") or 1)
        size = int(data.get("size") or 10)
        offset = (page - 1) * size
        collection_name = "trust_fund_v2"
        client_and_currency_match_condition = [{"$match": {"$and": []}}] if client or currency else []
        client_and_currency_match_condition[0]["$match"]["$and"].append(
            {"payment_type": {"$nin": ["Protected Deposit - Applied", "Gift Voucher", ""]}}
        )

        client_and_currency_match_condition.append(
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "let": {"current_client_id": "$client_id", "current_booking_ref": "$booking_ref"},
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$client_id", "$$current_client_id"]},
                                        {"$eq": ["$booking_ref", "$$current_booking_ref"]},
                                        {"$eq": ["$deleted", False]},
                                        {"$in": ["$payment_type", ["Protected Deposit - Applied", "Gift Voucher", ""]]},
                                    ]
                                }
                            }
                        },
                        {"$limit": 1},
                    ],
                    "as": "restricted_bank_records",
                }
            }
        )

        client_and_currency_match_condition.append(
            {
                "$match": {
                    "$or": [
                        {"restricted_bank_records": {"$eq": []}},
                        {"booking_ref": {"$in": ["A420005", "A459418", "A470309"]}},
                    ]
                }
            }
        )

        if client:
            client_and_currency_match_condition[0]["$match"]["$and"].append(
                {
                    "$or": [
                        {"return_date": {"$gte": "2020-01-01"}},
                        {"booking_ref": {"$in": ["A420005", "A459418", "A470309"]}},
                    ]
                }
            )
            client_and_currency_match_condition[0]["$match"]["$and"].append({"client_id": ObjectId(client)})

        if currency:
            client_and_currency_match_condition[0]["$match"]["$and"].append({"currency_code": {"$all": [currency]}})
        data = get_db()[collection_name].aggregate(
            [
                *client_and_currency_match_condition,
                {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
                {"$sort": {"balance": 1}},
                {"$skip": offset},
                {"$limit": size},
                {
                    "$lookup": {
                        "from": "client_basic_info",
                        "localField": "client_id",
                        "foreignField": "_id",
                        "as": "client",
                    }
                },
                {"$unwind": "$client"},
                {
                    "$project": {
                        "_id": "$_id",
                        "clientId": "$client._id",
                        "cId": "$client.c_id",
                        "clientName": "$client.full_name",
                        "friendlyName": "$client.friendly_name",
                        "bookingRef": "$booking_ref",
                        "leadPax": "$lead_pax",
                        "departureDate": "$departure_date",
                        "returnDate": "$return_date",
                        "bookingDate": "$booking_date",
                        **currency_projection_criteria,
                        "totalBanked": {"$ifNull": ["$total_in_trust", 0.0]},
                        "totalClaimed": {"$ifNull": ["$total_claimed", 0.0]},
                        "balance": {"$ifNull": ["$balance", 0.0]},
                        "totalBookingValue": {"$ifNull": ["$total_booking_value", 0.0]},
                        "type": {"$arrayElemAt": ["$bank.type", 0]},
                        "status": {"$ifNull": ["$booking_status", "Active"]},
                        "total": "$totalCount",
                    }
                },
            ],
            collation={"locale": "en", "strength": 1},
            allowDiskUse=True,
        )
        total_elements = get_db()[collection_name].aggregate(
            [
                {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
                *client_and_currency_match_condition,
                {"$count": "count"},
            ]
        )
        total_elements = next(total_elements, {"count": 0})["count"]

        content = self.__transform_tbr_content(data, client)
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil(((total_elements) / size)) else False
        total_pages = 0
        total_pages = ceil((total_elements) / size)

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def trust_balance_report(self, data):
        currency_projection_criteria = {"currency": "$currency_code"}
        client = data.get("client")
        currency = data.get("currency") or ""
        page = int(data.get("page") or 1)
        size = int(data.get("size") or 10)
        offset = (page - 1) * size
        client_list = [
            current_app.config.get("BARRHEAD"),
            current_app.config.get("HAYS"),
            current_app.config.get("SUNSHINE"),
            current_app.config.get("BROADWAY"),
        ]
        collection_name = "trust_fund" if client == os.environ.get("NAS") else "trust_fund_v2"
        client_and_currency_match_condition = [{"$match": {"$and": []}}] if client or currency else []
        if client:
            if client == current_app.config.get("NAS"):
                currency_projection_criteria = {
                    "currency": [{"$arrayElemAt": [{"$arrayElemAt": ["$bank.currency_code", -1]}, -1]}]
                }
            client_and_currency_match_condition[0]["$match"]["$and"].append({"client_id": ObjectId(client)})
            if client in client_list:
                if client == current_app.config.get("BARRHEAD"):
                    client_and_currency_match_condition[0]["$match"]["$and"].append(
                        {
                            "$or": [
                                {"return_date": {"$gte": "2020-01-01"}},
                                {
                                    "return_date": {
                                        "$gte": "1900-01-01",
                                        "$lte": "1900-12-31",
                                    }
                                },
                            ],
                            "booking_ref": {"$nin": ["********", "********", "********"]},
                        }
                    )
                else:
                    client_and_currency_match_condition[0]["$match"]["$and"].append(
                        {"return_date": {"$gte": "2020-01-01"}}
                    )

        if currency:
            client_and_currency_match_condition[0]["$match"]["$and"].append({"currency_code": {"$all": [currency]}})
        data = get_db()[collection_name].aggregate(
            [
                *client_and_currency_match_condition,
                {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
                {"$sort": {"balance": 1}},
                {"$skip": offset},
                {"$limit": size},
                {
                    "$lookup": {
                        "from": "banking_file_details",
                        "let": {"current_client_id": "$client_id"},
                        "localField": "booking_ref",
                        "foreignField": "booking_ref",
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": {
                                        "$and": [
                                            {"$eq": ["$client_id", "$$current_client_id"]},
                                            {"$eq": ["$deleted", False]},
                                        ]
                                    }
                                }
                            },
                            {"$sort": {"updated_at": 1}},
                            {
                                "$group": {
                                    "_id": None,
                                    "type": {"$last": "$type"},
                                    "refund": {"$sum": {"$cond": [{"$lt": ["$amount", 0]}, "$amount", 0]}},
                                    "currency_code": {"$push": "$currency_code"},
                                }
                            },
                        ],
                        "as": "bank",
                    }
                },
                {
                    "$lookup": {
                        "from": "client_basic_info",
                        "localField": "client_id",
                        "foreignField": "_id",
                        "as": "client",
                    }
                },
                {"$unwind": "$client"},
                {
                    "$project": {
                        "_id": "$_id",
                        "clientId": "$client._id",
                        "cId": "$client.c_id",
                        "clientName": "$client.full_name",
                        "friendlyName": "$client.friendly_name",
                        "bookingRef": "$booking_ref",
                        "leadPax": "$lead_pax",
                        "departureDate": "$departure_date",
                        "returnDate": "$return_date",
                        "bookingDate": "$booking_date",
                        **currency_projection_criteria,
                        "totalBanked": {"$ifNull": ["$total_in_trust", 0.0]},
                        "totalClaimed": {"$ifNull": ["$total_claimed", 0.0]},
                        "balance": {"$ifNull": ["$balance", 0.0]},
                        "totalBookingValue": {"$ifNull": ["$total_booking_value", 0.0]},
                        "refundsFromDepositFile": {"$ifNull": [{"$abs": {"$arrayElemAt": ["$bank.refund", 0]}}, 0]},
                        "deposits": {
                            "$add": [
                                {"$ifNull": [{"$abs": {"$arrayElemAt": ["$bank.refund", 0]}}, 0]},
                                {"$ifNull": ["$total_in_trust", 0]},
                            ]
                        },
                        "type": {"$arrayElemAt": ["$bank.type", 0]},
                        "status": {"$ifNull": ["$booking_status", "Active"]},
                        "total": "$totalCount",
                    }
                },
            ],
            collation={"locale": "en", "strength": 1},
            allowDiskUse=True,
        )
        total_elements = get_db()[collection_name].aggregate(
            [
                {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
                *client_and_currency_match_condition,
                {"$count": "count"},
            ]
        )
        total_elements = next(total_elements, {"count": 0})["count"]

        content = self.__transform_tbr_content(data, client)

        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil(((total_elements) / size)) else False
        total_pages = 0
        total_pages = ceil((total_elements) / size)

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def multi_currency_trust_balance_report(self, data):
        client = data.get("client")
        currency = data.get("currency") or ""
        page = int(data.get("page") or 1)
        size = int(data.get("size") or 10)
        offset = (page - 1) * size
        collection_name = "trust_fund" if client == os.environ.get("NAS") else "trust_fund_v2"
        client_and_currency_match_condition = [{"$match": {"$and": []}}] if client or currency else []
        if client:
            client_and_currency_match_condition[0]["$match"]["$and"].append({"client_id": ObjectId(client)})
        if currency:
            client_and_currency_match_condition[0]["$match"]["$and"].append({"currency_code": {"$all": [currency]}})
        data = list(
            get_db()[collection_name].aggregate(
                [
                    {"$unwind": "$currency_code"},
                    *client_and_currency_match_condition,
                    {
                        "$lookup": {
                            "from": "banking_file_details",
                            "let": {"current_client_id": "$client_id", "current_currency": "$currency_code"},
                            "localField": "booking_ref",
                            "foreignField": "booking_ref",
                            "pipeline": [
                                {
                                    "$match": {
                                        "$expr": {
                                            "$and": [
                                                {"$eq": ["$client_id", "$$current_client_id"]},
                                                {"$eq": ["$deleted", False]},
                                                {"$eq": ["$currency_code", "$$current_currency"]},
                                            ]
                                        }
                                    }
                                },
                                {
                                    "$group": {
                                        "_id": {"currency": "$currency_code", "booking_ref": "$booking_ref"},
                                        "total_banked": {"$sum": "$amount"},
                                        "refund": {"$sum": {"$cond": [{"$lt": ["$amount", 0]}, "$amount", 0]}},
                                        "booking_ref": {"$first": "$booking_ref"},
                                        "type": {"$last": "$type"},
                                        "currency": {"$last": "$currency_code"},
                                    }
                                },
                            ],
                            "as": "bank",
                        }
                    },
                    {"$unwind": {"path": "$bank", "preserveNullAndEmptyArrays": True}},
                    {
                        "$lookup": {
                            "from": "claims_file_details",
                            "localField": "booking_ref",
                            "let": {"current_client_id": "$client_id", "current_currency": "$currency_code"},
                            "foreignField": "booking_ref",
                            "pipeline": [
                                {
                                    "$match": {
                                        "$expr": {
                                            "$and": [
                                                {"$eq": ["$client_id", "$$current_client_id"]},
                                                {"$eq": ["$deleted", False]},
                                                {"$eq": ["$currency_code", "$$current_currency"]},
                                            ]
                                        }
                                    }
                                },
                                {
                                    "$group": {
                                        "_id": {"currency": "$currency_code", "booking_ref": "$booking_ref"},
                                        "total_claimed": {"$sum": "$amount"},
                                    }
                                },
                            ],
                            "as": "claims",
                        }
                    },
                    {"$unwind": {"path": "$claims", "preserveNullAndEmptyArrays": True}},
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {
                        "$addFields": {
                            "balance": {
                                "$subtract": [
                                    {"$ifNull": ["$bank.total_banked", 0.0]},
                                    {"$ifNull": ["$claims.total_claimed", 0.0]},
                                ]
                            },
                        }
                    },
                    {
                        "$match": {
                            "$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}],
                        }
                    },
                    {"$unwind": "$client"},
                    {"$sort": {"balance": 1}},
                    {
                        "$project": {
                            "_id": "$_id",
                            "clientId": "$client._id",
                            "cId": "$client.c_id",
                            "clientName": "$client.full_name",
                            "friendlyName": "$client.friendly_name",
                            "bookingRef": "$booking_ref",
                            "leadPax": "$lead_pax",
                            "departureDate": "$departure_date",
                            "returnDate": "$return_date",
                            "bookingDate": "$booking_date",
                            "totalBanked": {"$ifNull": ["$bank.total_banked", 0.0]},
                            "currency": "$currency_code",
                            "totalClaimed": {"$ifNull": ["$claims.total_claimed", 0.0]},
                            "totalBookingValue": {"$ifNull": ["$total_booking_value", 0.0]},
                            "status": {"$ifNull": ["$booking_status", "Active"]},
                            "bookingId": "$booking_id",
                            "refundsFromDepositFile": {"$ifNull": [{"$abs": "$bank.refund"}, 0.0]},
                            "deposits": {
                                "$add": [
                                    {"$ifNull": [{"$abs": "$bank.refund"}, 0.0]},
                                    {"$ifNull": ["$total_in_trust", 0.0]},
                                ]
                            },
                            "balance": {"$ifNull": ["$balance", 0.0]},
                            "type": "$bank.type",
                        }
                    },
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
                allowDiskUse=True,
            )
        )
        content = [{**row, "_id": str(row["_id"]), "clientId": str(row["clientId"])} for row in data[0]["data"]]
        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil(((total_elements) / size)) else False
        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def trust_balance_report_nas(self, data):
        client_id = data.get("client")
        if not client_id:
            raise ServiceException("Client Id is missing")
        currency = data.get("currency") or ""
        collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
        client_and_currency_match_condition = [{"$match": {"$and": [{"client_id": ObjectId(client_id)}]}}]
        if currency:
            client_and_currency_match_condition[0]["$match"]["$and"].append({"currency_code": {"$all": [currency]}})
        trust_funds = get_db()[collection_name].aggregate(
            [
                *client_and_currency_match_condition,
                {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
                {
                    "$lookup": {
                        "from": "banking_file_details",
                        "let": {"current_client_id": "$client_id"},
                        "localField": "booking_ref",
                        "foreignField": "booking_ref",
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": {
                                        "$and": [
                                            {"$eq": ["$client_id", "$$current_client_id"]},
                                            {"$eq": ["$deleted", False]},
                                        ]
                                    }
                                }
                            },
                            {
                                "$project": {
                                    "amount": 1,
                                    "type": {"$ifNull": ["$type", None]},
                                    "supplierNames": {"$ifNull": ["$supplier_names", None]},
                                }
                            },
                        ],
                        "as": "bank",
                    }
                },
                {
                    "$lookup": {
                        "from": "claims_file_details",
                        "let": {"current_client_id": "$client_id"},
                        "localField": "booking_ref",
                        "foreignField": "booking_ref",
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": {
                                        "$and": [
                                            {"$eq": ["$client_id", "$$current_client_id"]},
                                            {"$eq": ["$deleted", False]},
                                        ]
                                    }
                                }
                            },
                        ],
                        "as": "claim",
                    }
                },
                {
                    "$unionWith": {
                        "coll": "banking_metadata",
                        "pipeline": [
                            {
                                "$match": {
                                    "client_id": ObjectId(client_id),
                                    "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                                },
                            },
                            {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                            {"$sort": {"recent_file.submitted_date": -1}},
                            {"$limit": 1},
                            {
                                "$project": {
                                    "_id": 0,
                                    "file_id": "$recent_file.file_id",
                                    "submitted_date": "$recent_file.submitted_date",
                                }
                            },
                            {
                                "$lookup": {
                                    "from": "claims_metadata",
                                    "localField": "booking_ref",
                                    "foreignField": "booking_ref",
                                    "pipeline": [
                                        {
                                            "$match": {
                                                "client_id": ObjectId(client_id),
                                                "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                                            },
                                        },
                                        {"$addFields": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
                                        {"$sort": {"recent_file.submitted_date": -1}},
                                        {"$limit": 1},
                                        {
                                            "$project": {
                                                "_id": 0,
                                                "file_id": "$recent_file.file_id",
                                                "submitted_date": "$recent_file.submitted_date",
                                            }
                                        },
                                    ],
                                    "as": "claim",
                                }
                            },
                            {"$unwind": "$claim"},
                            {"$match": {"$expr": {"$lt": ["$claim.submitted_date", "$submitted_date"]}}},
                            {
                                "$lookup": {
                                    "from": "banking_file_details",
                                    "localField": "file_id",
                                    "foreignField": "file_id",
                                    "pipeline": [
                                        {"$match": {"deleted": False}},
                                        {
                                            "$group": {
                                                "_id": "$booking_ref",
                                                "client_id": {"$first": "$client_id"},
                                            }
                                        },
                                        {"$project": {"client_id": 1, "booking_ref": "$_id"}},
                                    ],
                                    "as": "bank",
                                }
                            },
                            {"$unwind": "$bank"},
                            {"$project": {"booking_ref": "$bank.booking_ref"}},
                            {
                                "$lookup": {
                                    "from": "trust_fund_v2",
                                    "localField": "booking_ref",
                                    "foreignField": "booking_ref",
                                    "pipeline": [
                                        *client_and_currency_match_condition,
                                        {
                                            "$match": {
                                                "$and": [{"balance": {"$lt": 0.005}}, {"balance": {"$gt": -0.005}}]
                                            }
                                        },
                                    ],
                                    "as": "trust_fund",
                                }
                            },
                            {"$unwind": "$trust_fund"},
                            {
                                "$lookup": {
                                    "from": "banking_file_details",
                                    "localField": "booking_ref",
                                    "foreignField": "booking_ref",
                                    "pipeline": [
                                        {
                                            "$match": {
                                                "client_id": ObjectId(client_id),
                                                "deleted": False,
                                            }
                                        },
                                        {"$sort": {"created_at": 1}},
                                        {
                                            "$project": {
                                                "amount": 1,
                                                "type": {"$ifNull": ["$type", None]},
                                                "supplierNames": {"$ifNull": ["$supplier_names", None]},
                                            }
                                        },
                                    ],
                                    "as": "bank",
                                }
                            },
                            {
                                "$lookup": {
                                    "from": "claims_file_details",
                                    "localField": "booking_ref",
                                    "foreignField": "booking_ref",
                                    "pipeline": [
                                        {
                                            "$match": {
                                                "client_id": ObjectId(client_id),
                                                "deleted": False,
                                            }
                                        },
                                        {"$sort": {"created_at": 1}},
                                    ],
                                    "as": "claim",
                                }
                            },
                            {
                                "$project": {
                                    "booking_ref": "$trust_fund.booking_ref",
                                    "pax_count": {"$toInt": {"$ifNull": ["$trust_fund.pax_count", 0]}},
                                    "lead_pax": "$trust_fund.lead_pax",
                                    "total_booking_value": {"$ifNull": ["$trust_fund.total_booking_value", 0.0]},
                                    "bonding": "$trust_fund.bonding",
                                    "booking_date": "$trust_fund.booking_date",
                                    "departure_date": "$trust_fund.departure_date",
                                    "return_date": "$trust_fund.return_date",
                                    "booking_status": "$trust_fund.booking_status",
                                    "currency_code": "$trust_fund.currency_code",
                                    "client_id": "$trust_fund.client_id",
                                    "bank": 1,
                                    "claim": 1,
                                }
                            },
                        ],
                    }
                },
                {
                    "$lookup": {
                        "from": "client_basic_info",
                        "localField": "client_id",
                        "foreignField": "_id",
                        "as": "client",
                    }
                },
                {"$unwind": "$client"},
                {
                    "$project": {
                        "_id": 0,
                        "clientId": "$client._id",
                        "cId": "$client.c_id",
                        "clientName": "$client.full_name",
                        "friendlyName": "$client.friendly_name",
                        "bookingRef": "$booking_ref",
                        "leadPax": "$lead_pax",
                        "departureDate": "$departure_date",
                        "returnDate": "$return_date",
                        "bookingDate": "$booking_date",
                        "payment_id": "$bank._id",
                        "banking_amount": "$bank.amount",
                        "supplierNames": "$bank.supplierNames",
                        "claim_amount": "$claim.amount",
                        "bookingStatus": "$booking_status",
                        "bookingId": "$booking_id",
                        "balance": {"$ifNull": ["$balance", 0.0]},
                        "currency": {"$last": "$currency_code"},
                        "types": "$bank.type",
                        "totalBookingValue": {"$ifNull": ["$total_booking_value", 0.0]},
                    }
                },
            ],
            collation={"locale": "en", "strength": 1},
            allowDiskUse=True,
        )
        output_data = self.__transform_tbr_content_nas(trust_funds)
        return {"content": output_data}

    def trust_balance_report_progress(self, data):
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        report = get_db().report_files.find_one(
            {"name": "TrustBalance", "status": "Generating New Report", "updated_at": {"$gte": one_hour_ago}},
            sort=[("updated_at", -1)],
        )

        if report:
            client_id = report.get("client_id")
            client_info = get_db().client_basic_info.find_one({"_id": client_id}, {"full_name": 1})
            full_name = client_info.get("full_name", "") if client_info else ""
            result = {"in_progress": True, "client_id": str(client_id), "full_name": full_name}
        else:
            result = {"in_progress": False}
        return result

    def client_files_report(self, data):
        client_id = data.get("client")
        from_date = data.get("fromDate") or None
        to_date = data.get("toDate") or None
        if from_date:
            from_date = datetime.fromisoformat(from_date)
        else:
            from_date = datetime.min
        if to_date:
            to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())
        else:
            to_date = datetime.max
        page = int(data.get("page") or 1)
        size = int(data.get("size") or 10)
        offset = (page - 1) * size

        client_match_condition = [{"client_id": ObjectId(client_id)}] if client_id else []

        data = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$addFields": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
                    {"$addFields": {"file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}}},
                    {
                        "$match": {
                            "$and": [
                                *client_match_condition,
                                {"file_datetime": {"$gte": from_date, "$lte": to_date}},
                            ]
                        }
                    },
                    {
                        "$lookup": {
                            "from": "claims_file_details",
                            "localField": "recent_file.file_id",
                            "foreignField": "file_id",
                            "pipeline": [
                                {
                                    "$group": {
                                        "_id": {"currency": "$currency_code", "deleted": "$deleted"},
                                        "sum": {"$sum": "$amount"},
                                        "count": {"$count": {}},
                                    }
                                },
                                {
                                    "$group": {
                                        "_id": {"currency": "$_id.currency"},
                                        "sum": {"$push": {"k": {"$toString": "$_id.deleted"}, "v": "$sum"}},
                                        "count": {"$push": {"k": {"$toString": "$_id.deleted"}, "v": "$count"}},
                                    }
                                },
                                {
                                    "$project": {
                                        "_id": "$_id",
                                        "sum": {"$arrayToObject": "$sum"},
                                        "count": {"$arrayToObject": "$count"},
                                    }
                                },
                            ],
                            "as": "claims",
                        }
                    },
                    {"$unwind": "$claims"},
                    {
                        "$addFields": {
                            "currency": {
                                "$reduce": {
                                    "input": "$claims.currency",
                                    "initialValue": [],
                                    "in": {"$setUnion": ["$$value", "$$this"]},
                                }
                            },
                        }
                    },
                    {
                        "$lookup": {
                            "from": "client_basic_info",
                            "localField": "client_id",
                            "foreignField": "_id",
                            "as": "client",
                        }
                    },
                    {"$unwind": "$client"},
                    {
                        "$project": {
                            "_id": "$_id",
                            "clientName": "$client.full_name",
                            "friendlyName": "$client.friendly_name",
                            "clientId": "$client.c_id",
                            "fileDate": "$recent_file.file_date",
                            "fileStatus": "$recent_file.status",
                            "fileType": "Claim File",
                            "fileName": "$recent_file.file_name",
                            "fileSubmitted": "$recent_file.submitted_date",
                            "claims": "$claims",
                            "currency": "$claims._id.currency",
                        }
                    },
                    {
                        "$unionWith": {
                            "coll": "banking_metadata",
                            "pipeline": [
                                {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                                {
                                    "$addFields": {
                                        "file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}
                                    }
                                },
                                {
                                    "$match": {
                                        "$and": [
                                            *client_match_condition,
                                            {"file_datetime": {"$gte": from_date, "$lte": to_date}},
                                        ]
                                    }
                                },
                                {
                                    "$lookup": {
                                        "from": "banking_file_details",
                                        "localField": "recent_file.file_id",
                                        "foreignField": "file_id",
                                        "pipeline": [
                                            {
                                                "$group": {
                                                    "_id": {"currency": "$currency_code", "deleted": "$deleted"},
                                                    "sum": {"$sum": "$amount"},
                                                    "count": {"$count": {}},
                                                }
                                            },
                                            {
                                                "$group": {
                                                    "_id": {"currency": "$_id.currency"},
                                                    "sum": {"$push": {"k": {"$toString": "$_id.deleted"}, "v": "$sum"}},
                                                    "count": {
                                                        "$push": {"k": {"$toString": "$_id.deleted"}, "v": "$count"}
                                                    },
                                                }
                                            },
                                            {
                                                "$project": {
                                                    "_id": "$_id",
                                                    "sum": {"$arrayToObject": "$sum"},
                                                    "count": {"$arrayToObject": "$count"},
                                                }
                                            },
                                        ],
                                        "as": "bank",
                                    }
                                },
                                {"$unwind": "$bank"},
                                {
                                    "$lookup": {
                                        "from": "client_basic_info",
                                        "localField": "client_id",
                                        "foreignField": "_id",
                                        "as": "client",
                                    }
                                },
                                {"$unwind": "$client"},
                                {
                                    "$project": {
                                        "_id": "$_id",
                                        "clientName": "$client.full_name",
                                        "friendlyName": "$client.friendly_name",
                                        "clientId": "$client.c_id",
                                        "fileDate": "$recent_file.file_date",
                                        "fileStatus": "$recent_file.status",
                                        "fileType": "Banking File",
                                        "fileName": "$recent_file.file_name",
                                        "fileSubmitted": "$recent_file.submitted_date",
                                        "bank": "$bank",
                                        "currency": "$bank._id.currency",
                                    }
                                },
                            ],
                        }
                    },
                    {"$sort": {"fileSubmitted": -1, "currency": 1}},
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ]
            )
        )

        content = []
        for row in data[0]["data"]:
            claim_file_total = 0
            claim_items = 0
            void_claim_amount = 0
            valid_claim_amount = 0
            void_claim_items = 0
            valid_claim_items = 0
            banking_file_total = 0
            banking_items = 0

            if row["fileType"] == "Claim File":
                claim_file_total = sum(row["claims"]["sum"].values())
                claim_items = sum(row["claims"]["count"].values())
                void_claim_amount = row["claims"]["sum"].get("true", 0)
                valid_claim_amount = row["claims"]["sum"].get("false", 0)
                void_claim_items = row["claims"]["count"].get("true", 0)
                valid_claim_items = row["claims"]["count"].get("false", 0)

            else:
                banking_file_total = sum(row["bank"]["sum"].values())
                banking_items = sum(row["bank"]["count"].values())

            file_dict = {
                "_id": str(row["_id"]),
                "clientName": row["clientName"],
                "friendlyName": row["friendlyName"],
                "fileDate": row.get("fileDate"),
                "fileStatus": row.get("fileStatus"),
                "fileType": row.get("fileType"),
                "fileName": row.get("fileName"),
                "fileSubmitted": row.get("fileSubmitted").isoformat(),
                "validClaimItems": valid_claim_items,
                "voidClaimItems": void_claim_items,
                "claimItems": claim_items,
                "validClaimAmount": valid_claim_amount,
                "voidClaimAmount": void_claim_amount,
                "claimFileTotal": claim_file_total,
                "bankingItems": banking_items,
                "bankingFileTotal": banking_file_total,
                "currency": row["currency"],
            }
            content.append(file_dict)

        empty = True if not content else False
        first = True if page == 1 else False
        last = True if data[0]["metadata"] and page == ceil((data[0]["metadata"][0]["total"]) / size) else False
        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def banking_and_claim_between_dates(self, client_id, currency, from_date, to_date, weekly_fornightly=False):
        banking_amount = 0
        claim_amount = 0
        if from_date:
            from_date = datetime.fromisoformat(from_date)
        else:
            from_date = datetime.min
        if to_date:
            to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())
        else:
            to_date = datetime.max

        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        if not basic_info:
            raise ServiceException("Client not found")
        type_of_trust_account = get_db().lookup_trust_type.find_one({"_id": basic_info["type_of_trust_account"]})
        trust_type = type_of_trust_account["name"] if type_of_trust_account else None

        banking_data, claim_data = self.__movements_report_data(ObjectId(client_id), from_date, to_date)
        banking_amount = next(filter(lambda x: x["currencyCode"] == currency, banking_data), {"receipts": 0})[
            "receipts"
        ]
        claim_amount = next(filter(lambda x: x["currencyCode"] == currency, claim_data), {"payments": 0})["payments"]

        if weekly_fornightly and trust_type == "ATOL Escrow":
            amount = banking_amount - claim_amount
            if amount < 0:
                response = {"clientName": basic_info["full_name"], "bankingAmount": 0, "claimAmount": abs(amount)}
            else:
                response = {"clientName": basic_info["full_name"], "bankingAmount": amount, "claimAmount": 0}
        else:
            response = {
                "clientName": basic_info["full_name"],
                "bankingAmount": banking_amount,
                "claimAmount": claim_amount,
            }
        return response

    def export_weekly_fortnightly_report(self, weekly_data, open_banking_data, name, client, to_date, currency):
        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        lookup_currency = get_db().lookup_currency.find_one({"code": currency}, projection={"symbol": 1})
        symbol = lookup_currency["symbol"]
        today = time.strftime("%d/%m/%Y")

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()
        worksheet.set_column("A:A", 60)
        worksheet.set_column("B:B", 20)

        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        merge_left_1 = workbook.add_format({"size": 11, "align": "left"})
        merge_center = workbook.add_format({"bold": 1, "size": 11, "align": "center", "valign": "vcenter"})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
        amount_format_red = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "color": "red"}
        )
        cell_format = workbook.add_format()
        cell_format.set_text_wrap()
        worksheet.write("A1", f"{basic_info['full_name']}", merge_left)
        worksheet.write("A2", "Statement of Trust Account", merge_left)
        worksheet.write("A3", f"For the Compliance Reporting Day of {today}", merge_left)
        worksheet.write("B3", "Trust Account", merge_left)
        worksheet.write("B5", symbol, merge_center)
        if open_banking_data:
            worksheet.write("B6", open_banking_data["funds_in"], amount_format)
            worksheet.write(
                "A6",
                "Total amount that has been paid into the Trustee Account since\n the Prior Compliance Reporting Day",
                cell_format,
            )
            worksheet.write("B8", open_banking_data["funds_out"], amount_format_red)
            worksheet.write(
                "A8",
                "Total amount that has been paid out by the Trustee in response to\n Payment Requests since the Prior Compliance Reporting Day",
                cell_format,
            )

        worksheet.write(
            "A11", f"{basic_info['full_name']} certify this statement as being true and accurate", merge_left_1
        )
        worksheet.write("A13", "Signed", merge_left_1)
        worksheet.write("A17", "Position", merge_left_1)
        worksheet.write("A21", "Date", merge_left_1)
        workbook.close()

    def bank_reconciliation_statement_report(self, client, currency, from_date, to_date):
        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        claim_and_banking_amount = self.banking_and_claim_between_dates(client, currency, from_date, to_date)
        opening_date = datetime.fromisoformat(from_date)
        closing_date = datetime.fromisoformat(to_date)
        response = {}
        response.update(claim_and_banking_amount)
        opening_balance_details = get_db().opening_closing_balance.find_one(
            {"client_id": ObjectId(client), "currency": currency, "date": opening_date}
        )
        opening_balance = opening_balance_details.get("opening_balance") if opening_balance_details else 0

        closing_balance_details = get_db().opening_closing_balance.find_one(
            {"client_id": ObjectId(client), "currency": currency, "date": closing_date}
        )
        closing_balance = closing_balance_details.get("closing_balance") if closing_balance_details else 0

        client_not_banked_data = self.__summary_for_status(
            client, currency, from_date, to_date, status="Client Not Banked"
        )

        response.update(
            {
                "clientName": basic_info["full_name"],
                "bankingAmount": response["bankingAmount"] - client_not_banked_data["banking_amount"],
                "claimAmount": -(claim_and_banking_amount["claimAmount"] - client_not_banked_data["claim_amount"]),
                "openingBalance": opening_balance,
                "closingBalance": closing_balance
                - (client_not_banked_data["banking_amount"] - client_not_banked_data["claim_amount"]),
            }
        )
        return response

    def bluestyle_create_excel_file(self, payload, notes, from_date, to_date, file_name):
        opening_balance_ucb = payload.get("openingBalanceUCB", 0)
        opening_balance_rb = payload.get("openingBalanceRB", 0)
        receipts_ucb = payload.get("receiptsUCB", 0)
        receipts_rb = payload.get("receiptsRB", 0)
        payments_ucb = payload.get("paymentsUCB", 0)
        payments_rb = payload.get("paymentsRB", 0)
        bank_charges_ucb = payload.get("bankChargesUCB", 0)
        bank_charges_rb = payload.get("bankChargesRB", 0)
        closing_balance_as_per_statement_rb = payload.get("closingBalanceAsPerStatementRB", 0)
        closing_balance_as_per_statement_ucb = payload.get("closingBalanceAsPerStatementUCB", 0)
        deductibles = payload.get("deductibles", 0)
        notes = payload.get("statements")

        # Calculations
        opening_balance_total = opening_balance_ucb + opening_balance_rb
        total_receipts = receipts_ucb + receipts_rb
        total_bank_charges = bank_charges_rb + bank_charges_ucb
        total_payments = payments_ucb + payments_rb

        ucb_closing_balance = (opening_balance_ucb + receipts_ucb) - (payments_ucb + bank_charges_ucb)
        rb_closing_balance = opening_balance_rb + receipts_rb - payments_rb - bank_charges_rb
        total_closing_balance = ucb_closing_balance + rb_closing_balance

        difference_ucb = closing_balance_as_per_statement_ucb - (
            opening_balance_ucb - payments_ucb + receipts_ucb - bank_charges_rb
        )
        difference_rb = closing_balance_as_per_statement_rb - (
            opening_balance_rb + receipts_rb - payments_rb - bank_charges_rb
        )

        closing_balances_trust_accounts = closing_balance_as_per_statement_ucb + closing_balance_as_per_statement_rb
        deductible_differance = closing_balances_trust_accounts - deductibles

        # Create the Excel file

        from_date = change_date_format(from_date)
        numeric_from_date = format_date_from_numeric(from_date)
        to_date = change_date_format(to_date)
        numeric_to_date = format_date_from_numeric(to_date)

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")
        worksheet = workbook.add_worksheet("Bank Summary")
        notes_format = workbook.add_format({"italic": True, "font_size": 10, "align": "left"})
        from_date = format_date_with_day_ordinal(from_date)
        to_date = format_date_with_day_ordinal(to_date)

        worksheet.set_column("A:A", 60)
        worksheet.set_column("B:B", 20)
        worksheet.set_column("C:C", 20)
        worksheet.set_column("D:D", 20)

        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        merge_center = workbook.add_format({"bold": 1, "size": 11, "align": "center"})
        merge_left_1 = workbook.add_format({"size": 11, "align": "left"})
        merge_format = workbook.add_format({"align": "center", "valign": "vcenter", "bold": True, "border": 2})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
        amount_format_red = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "color": "red"}
        )
        cell_format = workbook.add_format()
        cell_format.set_text_wrap()

        warning_format = workbook.add_format()
        warning_format.set_text_wrap()
        warning_format.set_font_color("red")

        closing_balance_at_format = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "bottom": 6, "top": 1}
        )

        worksheet.write("A1", "ACT Azure Trust (for Blue Style a.s.)", merge_left)
        worksheet.write("A2", "Consolidated Statement of Trust Accounts", merge_left)
        worksheet.write("A3", f"For the Week Ending {to_date}", merge_left)
        worksheet.merge_range("B4:D4", "Trust Accounts", merge_format)
        worksheet.write("B5", "UniCredit Bank(UCB)", merge_center)
        worksheet.write("C5", "Raiffeisen Bank(RB)", merge_center)
        worksheet.write("D5", "Total", merge_center)
        worksheet.write("A6", "ACT Azure Trust Accounts", merge_left)
        worksheet.write("B6", "Czech Crowns", merge_center)
        worksheet.write("C6", "Czech Crowns", merge_center)
        worksheet.write("D6", "Czech Crowns", merge_center)

        line_no = 9
        counter = 2

        if payload:
            worksheet.write(
                "A7",
                f"Opening Balance as at {from_date}",
                cell_format,
            )
            worksheet.write("B7", opening_balance_ucb, amount_format)
            worksheet.write("C7", opening_balance_rb, amount_format)
            worksheet.write("D7", opening_balance_total, amount_format)
            worksheet.write(
                "A9",
                "Total  receipts into the Trustee Account during the week",
                cell_format,
            )
            worksheet.write("B9", receipts_ucb, amount_format)
            worksheet.write("C9", receipts_rb, amount_format)
            worksheet.write("D9", total_receipts, amount_format)
            line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                "Total payments made from the Trustee Account during the week",
                cell_format,
            )
            worksheet.write(f"B{line_no}", payments_ucb, amount_format_red)
            worksheet.write(f"C{line_no}", payments_rb, amount_format_red)
            worksheet.write(f"D{line_no}", total_payments, amount_format_red)
            line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                "Bank Charges",
                cell_format,
            )
            worksheet.write(f"B{line_no}", bank_charges_ucb, amount_format_red)
            worksheet.write(f"C{line_no}", bank_charges_rb, amount_format_red)
            worksheet.write(f"D{line_no}", total_bank_charges, amount_format_red)
            line_no = line_no + counter

            worksheet.write(
                f"A{line_no}",
                f"Closing Balance as at {to_date}",
                cell_format,
            )
            worksheet.write(f"B{line_no}", ucb_closing_balance, closing_balance_at_format)
            worksheet.write(f"C{line_no}", rb_closing_balance, closing_balance_at_format)
            worksheet.write(f"D{line_no}", total_closing_balance, closing_balance_at_format)
            line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                "Closing balance as per bank statement",
                merge_left,
            )
            worksheet.write(f"B{line_no}", ucb_closing_balance, amount_format)
            worksheet.write(f"C{line_no}", rb_closing_balance, amount_format)
            line_no = line_no + counter + 1
            worksheet.write(
                f"A{line_no}",
                "Difference",
                cell_format,
            )
            worksheet.write(f"B{line_no}", difference_ucb, amount_format)
            worksheet.write(f"C{line_no}", difference_rb, amount_format)
            line_no = line_no + counter + 1
            worksheet.write(
                f"A{line_no}",
                f"Deductables as per Revised Interim Settlement Policy for {numeric_from_date}",
                cell_format,
            )
            worksheet.write(f"D{line_no}", deductibles, amount_format)
            line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                "Closing Balances as per both Trust Accounts (UCB & RB)",
                merge_left,
            )
            worksheet.write(f"D{line_no}", closing_balances_trust_accounts, amount_format)
            # line_no = line_no + counter
            # worksheet.write(
            #     f"A{line_no}",
            #     "Difference",
            #     cell_format,
            # )
            # worksheet.write(f"D{line_no}", deductible_differance, amount_format)
            line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                "Explanation of Difference (if any)",
                merge_left,
            )
            line_no = line_no + counter
            notes = payload.get("statements")
            worksheet.write(
                f"A{line_no}",
                "Notes: ",
                merge_left,
            )

            all_notes = "\n".join(f"{idx}. {note}" for idx, note in enumerate(notes, start=1))
            worksheet.write(f"A{line_no}", all_notes, notes_format)
            notes_format.set_text_wrap()
            worksheet.set_row(line_no - 1, 140)

            worksheet.write(
                f"A{line_no + 2}", "PTT CZ s.r.o. certifies that this statement is true and accurate", merge_left_1
            )
            worksheet.write(f"A{line_no + 4}", "Signed", merge_left_1)
            image = "flaskr/assets/signature.png"

            if os.path.exists(image):
                try:
                    worksheet.insert_image(
                        f"A{line_no + 5}",
                        image,
                        {
                            "x_scale": 0.5,
                            "y_scale": 0.5,
                        },
                    )
                    print(f"Signature image successfully uploaded from {image}")
                except Exception as e:
                    print(f"Error uploading signature image: {e}")
            else:
                print(f"Image not found at {image}. Please ensure the image exists in the 'assets' folder.")

            worksheet.write(f"A{line_no + 10}", "Position", merge_left_1)
            worksheet.write(f"A{line_no + 11}", "Director & Trustee", merge_left_1)
            worksheet.write(f"A{line_no +15}", "Date", merge_left_1)

        workbook.close()

    def export_bank_reconciliation_statement_report(
        self, data, open_banking_data, hide_warnings, name, client, from_date, to_date, currency
    ):
        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        from_date = format_date_with_day_ordinal(from_date)
        to_date = format_date_with_day_ordinal(to_date)
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()
        worksheet.set_column("A:A", 60)
        worksheet.set_column("B:B", 20)
        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        merge_center = workbook.add_format({"bold": 1, "size": 11, "align": "center"})
        merge_left_1 = workbook.add_format({"size": 11, "align": "left"})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
        amount_format_red = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "color": "red"}
        )
        cell_format = workbook.add_format()
        cell_format.set_text_wrap()

        warning_format = workbook.add_format()
        warning_format.set_text_wrap()
        warning_format.set_font_color("red")

        closing_balance_at_format = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "bottom": 6, "top": 1}
        )

        worksheet.write("A1", f"{basic_info['full_name']}", merge_left)
        worksheet.write("A2", "Statement of Trust Account", merge_left)
        worksheet.write("A3", f"For the Month Ending {to_date}", merge_left)
        worksheet.write("B3", "Trust Account", merge_center)
        worksheet.write("B4", get_currency_symbol(currency), merge_center)

        line_no = 8
        counter = 2

        if open_banking_data:
            if open_banking_data.get("opening_balance"):
                worksheet.write(
                    "A6",
                    f"Opening Balance as at {from_date}",
                    cell_format,
                )
                worksheet.write("B6", open_banking_data.get("opening_balance"), amount_format)

            worksheet.write(
                "A8",
                "Total customer receipts into the Trustee Account during the month",
                cell_format,
            )
            worksheet.write("B8", open_banking_data["funds_in"], amount_format)
            line_no = line_no + counter
            if open_banking_data.get("transfer_from_deposit"):
                worksheet.write(
                    f"A{line_no}",
                    "Transfer from Deposit account",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data["transfer_from_deposit"], amount_format)
                line_no = line_no + counter
            if open_banking_data.get("bank_charges_reversal"):
                worksheet.write(
                    f"A{line_no}",
                    "Bank Charges Reversal",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data.get("bank_charges_reversal"), amount_format)
                line_no = line_no + counter
            if open_banking_data.get("interest_received"):
                worksheet.write(
                    f"A{line_no}",
                    "Interest Received on deposit account",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data.get("interest_received"), amount_format)
                line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                "Total payments made from the Trustee Account during the month",
                cell_format,
            )
            funds_out = -abs(open_banking_data["funds_out"])
            worksheet.write(f"B{line_no}", funds_out, amount_format_red)
            line_no = line_no + counter
            if open_banking_data.get("bank_fees"):
                worksheet.write(
                    f"A{line_no}",
                    "Bank Fees",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data.get("bank_fees"), amount_format_red)
                line_no = line_no + counter
            if open_banking_data.get("transfer_abtot"):
                worksheet.write(
                    f"A{line_no}",
                    "Transfer to ABTOT Trust",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data.get("transfer_abtot"), amount_format_red)
                line_no = line_no + counter
            if open_banking_data.get("bank_charges"):
                worksheet.write(
                    f"A{line_no}",
                    "Bank Charges",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data.get("bank_charges"), amount_format_red)
                line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                f"Closing Balance as at {to_date}",
                cell_format,
            )
            worksheet.write(f"B{line_no}", open_banking_data["closing_balance"], closing_balance_at_format)
            line_no = line_no + counter
            if open_banking_data.get("closing_balance_on_statement"):
                worksheet.write(
                    f"A{line_no}",
                    "Closing Balance as per bank statement",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data["closing_balance_on_statement"], amount_format)
                line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                "Difference",
                cell_format,
            )
            worksheet.write(f"B{line_no}", open_banking_data["difference"], amount_format)
            line_no = line_no + counter

            worksheet.write(f"A{line_no}", data["accountingStatement"], cell_format)
            worksheet.write(
                f"A{line_no + 2}",
                f"{basic_info['full_name']} certify this statement as being true and accurate",
                merge_left_1,
            )
            worksheet.write(f"A{line_no + 4}", "Signed", merge_left_1)
            worksheet.write(f"A{line_no + 8}", "Position", merge_left_1)
            worksheet.write(f"A{line_no + 12}", "Date", merge_left_1)

        workbook.close()

    def export_banking_summary_report(self, open_banking_data, name, client, from_date, to_date):
        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        bank_acct = get_db().client_bank_acct_details.find_one(
            {"client_id": ObjectId(client), "account_type": {"$in": ["Trust", "Business Account"]}}
        )
        depo_acct = get_db().client_bank_acct_details.find_one(
            {"client_id": ObjectId(client), "account_type": "Deposit"}
        )
        from_date = format_date_with_day_ordinal(from_date)
        to_date = format_date_with_day_ordinal(to_date)
        account_name = []
        if not open_banking_data:
            raise ServiceException("Open Banking data not found")
        else:
            for account in open_banking_data:
                account_type = account["accountType"].upper()
                account_name.append(account_type)
            workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")

            worksheet = workbook.add_worksheet("Current")
            worksheet.set_column("A:A", 20)
            worksheet.set_column("B:B", 60)
            worksheet.set_column("C:C", 40)
            worksheet.set_column("D:D", 30)
            worksheet.set_column("E:E", 20)
            worksheet.set_column("F:F", 20)
            worksheet.set_column("G:G", 20)
            worksheet.set_column("H:H", 20)
            merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
            merge_right = workbook.add_format({"size": 11, "align": "right"})
            amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "left", "valign": "vcenter"})
            amount_format_right = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
            closing_balance_at_format = workbook.add_format(
                {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "bottom": 6, "top": 1}
            )
            cell_format = workbook.add_format()
            cell_format.set_text_wrap()
            time_generated = datetime.utcnow()
            generation_timestamp = time_generated.strftime("%d-%m-%Y %H:%M")
            warning_format = workbook.add_format()
            warning_format.set_text_wrap()
            warning_format.set_font_color("red")

            worksheet.write("A2", f"Date and Time of generation of the document:{generation_timestamp}", merge_left)
            worksheet.write("A6", "Sort Code", merge_left)
            worksheet.write("A7", "90222", merge_right)
            worksheet.write("B6", "Account Number", merge_left)
            if bank_acct:
                worksheet.write("B7", f"{bank_acct['account_no']}", merge_right)
            else:
                worksheet.write("B7", "Nill", merge_right)
            worksheet.write("C6", "Account Type", merge_left)
            worksheet.write("C7", "Current & Deposit", merge_right)
            worksheet.write("D6", "Account Alias", merge_left)
            worksheet.write("D7", f"{basic_info['full_name']}", merge_right)
            worksheet.write("E6", "Currency", merge_left)
            if bank_acct:
                worksheet.write("E7", f"{bank_acct['currency']}", merge_right)
            else:
                worksheet.write("E7", "Nill", merge_right)

            worksheet.write("F6", "BIC", merge_left)
            worksheet.write("G6", "Last Statement date", merge_left)
            worksheet.write("G7", "02-09-2023", merge_right)
            worksheet.write("H6", "IBAN", merge_left)
            worksheet.write("H7", "GB 41 ABBY090222", merge_right)
            worksheet.write("A9", "Date", merge_left)
            worksheet.write("B9", "Narrative", merge_left)
            worksheet.write("C9", "Transation Type", merge_left)
            worksheet.write("D9", "Debit", merge_left)
            worksheet.write("E9", "Credit", merge_left)
            worksheet.write("F9", "Current Balance", merge_left)
            line_no = 10
            counter = 1
            if any(data in account_name for data in ["TRUST", "BUSINESS ACCOUNT"]) and open_banking_data:
                total_debit_amount = 0
                total_credit_amount = 0
                total_amount_multiple_payments = 0
                for data_item in open_banking_data:
                    account_type = data_item.get("accountType", "").upper()
                    if account_type in ["TRUST", "BUSINESS ACCOUNT"] and "transactions" in data_item:
                        transactions = data_item.get("transactions", [])
                        for transaction in transactions:
                            transaction_type = transaction.get("transaction_type", "").upper()
                            description = transaction.get("description", "").upper()
                            amount = transaction.get("amount", 0)
                            abs_amount = abs(amount)
                            timestamp = transaction.get("timestamp", "No timestamp available")
                            parsed_date = datetime.strptime(timestamp, "%Y-%m-%dT%H:%M:%SZ")
                            formatted_date = parsed_date.strftime("%d-%m-%Y")
                            meta = transaction.get("meta", {})
                            provider_category = meta.get("provider_category", "No provider_category available")
                            running_balance = transaction.get("running_balance", {})
                            balance_amount = running_balance.get("amount", "No amount available")

                            worksheet.write(f"A{line_no}", formatted_date, amount_format_right)
                            worksheet.write(f"B{line_no}", description, amount_format)
                            worksheet.write(f"C{line_no}", f"{provider_category} - {transaction_type}", amount_format)
                            if transaction_type == "DEBIT":
                                worksheet.write(f"D{line_no}", abs_amount, amount_format_right)
                                total_debit_amount += abs_amount
                            elif transaction_type == "CREDIT":
                                worksheet.write(f"E{line_no}", abs_amount, amount_format_right)
                                total_credit_amount += abs_amount
                            worksheet.write(f"F{line_no}", balance_amount, amount_format_right)

                            if description == "MULTIPLE PAYMENTS":
                                total_amount_multiple_payments += abs_amount

                            line_no += counter

                worksheet.write(f"D{line_no}", total_debit_amount, closing_balance_at_format)
                worksheet.write(f"E{line_no}", total_credit_amount, closing_balance_at_format)
                line_no += counter

            worksheet = workbook.add_worksheet("Deposit")
            worksheet.set_column("A:A", 20)
            worksheet.set_column("B:B", 60)
            worksheet.set_column("C:C", 40)
            worksheet.set_column("D:D", 30)
            worksheet.set_column("E:E", 20)
            worksheet.set_column("F:F", 20)
            worksheet.set_column("G:G", 20)
            worksheet.set_column("H:H", 20)
            merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
            merge_right = workbook.add_format({"size": 11, "align": "right"})
            amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "left", "valign": "vcenter"})
            amount_format_right = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
            closing_balance_at_format = workbook.add_format(
                {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "bottom": 6, "top": 1}
            )

            cell_format = workbook.add_format()
            cell_format.set_text_wrap()
            time_generated = datetime.utcnow()
            generation_timestamp = time_generated.strftime("%d-%m-%Y %H:%M")
            warning_format = workbook.add_format()
            warning_format.set_text_wrap()
            warning_format.set_font_color("red")

            worksheet.write("A2", f"Date and Time of generation of the document:{generation_timestamp}", merge_left)
            worksheet.write("A6", "Sort Code", merge_left)
            worksheet.write("A7", "90222", merge_right)
            worksheet.write("B6", "Account Number", merge_left)
            if depo_acct:
                worksheet.write("B7", f"{depo_acct['account_no']}", merge_right)
            else:
                worksheet.write("B7", "Nil", merge_right)
            worksheet.write("C6", "Account Type", merge_left)
            worksheet.write("C7", "Current & Deposit", merge_right)
            worksheet.write("D6", "Account Alias", merge_left)
            worksheet.write("D7", f"{basic_info['full_name']}", merge_right)
            worksheet.write("E6", "Currency", merge_left)
            if depo_acct:
                worksheet.write("E7", f"{depo_acct['currency']}", merge_right)
            else:
                worksheet.write("E7", "Nil", merge_right)

            worksheet.write("F6", "BIC", merge_left)
            worksheet.write("G6", "Last Statement date", merge_left)
            worksheet.write("G7", "02-09-2023", merge_right)
            worksheet.write("H6", "IBAN", merge_left)
            worksheet.write("H7", "GB 41 ABBY090222", merge_right)
            worksheet.write("A9", "Date", merge_left)
            worksheet.write("B9", "Narrative", merge_left)
            worksheet.write("C9", "Transation Type", merge_left)
            worksheet.write("D9", "Debit", merge_left)
            worksheet.write("E9", "Credit", merge_left)
            worksheet.write("F9", "Current Balance", merge_left)
            line_no = 10
            counter = 1
            if "DEPOSIT" in account_name and open_banking_data:
                total_debit_amount = 0
                total_credit_amount = 0
                total_amount_interest_de_payments = 0
                total_amount_interest_cre_payments = 0
                transfer_toFunds_deb = 0
                transfer_toFunds_cre = 0

                for data_item in open_banking_data:
                    account_type = data_item.get("accountType", "").upper()
                    if account_type == "DEPOSIT" and "transactions" in data_item:
                        transactions = data_item.get("transactions", [])
                        for transaction in transactions:
                            transaction_type = transaction.get("transaction_type", "").upper()
                            description = transaction.get("description", "").upper()
                            amount = transaction.get("amount", 0)
                            abs_amount = abs(amount)
                            timestamp = transaction.get("timestamp", "No timestamp available")
                            parsed_date = datetime.strptime(timestamp, "%Y-%m-%dT%H:%M:%SZ")
                            formatted_date = parsed_date.strftime("%d-%m-%Y")

                            worksheet.write(f"A{line_no}", formatted_date, amount_format_right)
                            worksheet.write(f"B{line_no}", description, amount_format)
                            meta = transaction.get("meta", {})
                            provider_category = meta.get("provider_category", "No provider_category available")

                            formatted_string = f"{provider_category} - {transaction_type}"
                            worksheet.write(f"C{line_no}", formatted_string, amount_format)

                            if transaction_type == "DEBIT":
                                worksheet.write(f"D{line_no}", abs_amount, amount_format_right)
                                total_debit_amount += abs_amount
                                if description.startswith("INTEREST PAID"):
                                    total_amount_interest_de_payments += abs_amount
                                """elif description.startswith("TRANSFER TO FUNDS"):
                                    transfer_toFunds_deb += abs_amount"""
                            elif transaction_type == "CREDIT":
                                worksheet.write(f"E{line_no}", abs_amount, amount_format_right)
                                total_credit_amount += abs_amount
                                if description.startswith("INTEREST PAID"):
                                    total_amount_interest_cre_payments += abs_amount
                                """elif description.startswith("TRANSFER TO FUNDS"):
                                    transfer_toFunds_cre += abs_amount"""

                            if description.startswith("INTEREST PAID"):
                                line_no += counter
                                continue

                            transfer_toFunds_deb = total_debit_amount - total_amount_interest_de_payments
                            transfer_toFunds_cre = total_credit_amount - total_amount_interest_cre_payments
                            running_balance = transaction.get("running_balance", {})
                            balance_amount = running_balance.get("amount", "No amount available")
                            worksheet.write(f"F{line_no}", balance_amount, amount_format_right)

                            line_no += counter

                worksheet.write(f"D{line_no}", total_debit_amount, closing_balance_at_format)

                worksheet.write(f"E{line_no}", total_credit_amount, closing_balance_at_format)
                line_no += counter + 1

                worksheet.write(f"C{line_no}", "Interest", cell_format)
                worksheet.write(f"D{line_no}", total_amount_interest_de_payments, amount_format_right)
                worksheet.write(f"E{line_no}", total_amount_interest_cre_payments, amount_format_right)
                line_no += counter

                worksheet.write(f"C{line_no}", "Interest Adjustment", cell_format)
                worksheet.write(f"D{line_no}", "0.00", amount_format_right)
                worksheet.write(f"E{line_no}", "0.00", amount_format_right)
                line_no += counter

                worksheet.write(f"C{line_no}", "Transfer of funds", cell_format)
                worksheet.write(f"D{line_no}", transfer_toFunds_deb, amount_format_right)
                worksheet.write(f"E{line_no}", transfer_toFunds_cre, amount_format_right)
                line_no += counter + 1

            workbook.close()
            # sftp_bucket = current_app.config["SFTP_BUCKET"]
            sftp_bucket = current_app.config["PTT_BUCKET"]
            s3_key = f'{"banking_summary_report/"}{name}'
            upload_file(bucket=sftp_bucket, key=s3_key, file_path=f"{current_app.config['TEMP_DIR']}/{name}")

    def movements_of_funds(self, client_id, from_date, to_date, previous_from_date, previous_to_date):
        receipts_current, payments_current = self.__movements_report_data(
            ObjectId(client_id), from_date, to_date, "Current"
        )

        receipts_previous, payments_previous = self.__movements_report_data(
            ObjectId(client_id), previous_from_date, previous_to_date, "Previous"
        )
        from_date = datetime.fromisoformat(from_date)
        to_date = datetime.fromisoformat(to_date)
        response_dict = {}
        default_values = {
            "openingBalanceCurrent": 0,
            "receiptsCurrent": 0,
            "paymentsCurrent": 0,
            "closingBalanceCurrent": 0,
            "openingBalancePrevious": 0,
            "receiptsPrevious": 0,
            "paymentsPrevious": 0,
            "closingBalancePrevious": 0,
            "openingBalanceComparison": 0,
            "receiptsComparison": 0,
            "paymentsComparison": 0,
            "closingBalanceComparison": 0,
            "openingBalanceCurrentThousandth": 0,
            "receiptsCurrentThousandth": 0,
            "paymentsCurrentThousandth": 0,
            "closingBalanceCurrentThousandth": 0,
            "openingBalancePreviousThousandth": 0,
            "receiptsPreviousThousandth": 0,
            "paymentsPreviousThousandth": 0,
            "closingBalancePreviousThousandth": 0,
        }
        for item in receipts_current:
            logging.info(f"Processing receipt for {item['clientId']}_{item['currencyCode']}")
            response_dict[f"{item['clientId']}_{item['currencyCode']}"] = {**default_values, **item}
        for item in receipts_previous:
            if f"{item['clientId']}_{item['currencyCode']}" in response_dict.keys():
                response_dict[f"{item['clientId']}_{item['currencyCode']}"].update(item)
            else:
                response_dict[f"{item['clientId']}_{item['currencyCode']}"] = {**default_values, **item}

        for item in payments_current:
            logging.info(f"Processing payment for {item['clientId']}_{item['currencyCode']}")
            if f"{item['clientId']}_{item['currencyCode']}" in response_dict.keys():
                response_dict[f"{item['clientId']}_{item['currencyCode']}"].update(item)
            else:
                response_dict[f"{item['clientId']}_{item['currencyCode']}"] = {**default_values, **item}

        for item in payments_previous:
            logging.info(f"Processing previous payment for {item['clientId']}_{item['currencyCode']}")
            if f"{item['clientId']}_{item['currencyCode']}" in response_dict.keys():
                response_dict[f"{item['clientId']}_{item['currencyCode']}"].update(item)
            else:
                response_dict[f"{item['clientId']}_{item['currencyCode']}"] = {**default_values, **item}
        response = list(response_dict.values())
        for data in response:
            data["receiptsComparison"] = (
                data["receiptsCurrent"] / data["receiptsPrevious"] if data["receiptsPrevious"] != 0 else 0  # NaN
            )
            data["paymentsComparison"] = (
                data["paymentsCurrent"] / data["paymentsPrevious"] if data["paymentsPrevious"] != 0 else 0  # NaN
            )
        current_to_date = datetime.fromisoformat(str(to_date.date()))
        previous_to_date = datetime.fromisoformat(str(previous_to_date.date()))
        date_list = [from_date, current_to_date, previous_from_date, previous_to_date]
        balance_list = list(get_db().opening_closing_balance.find({"date": {"$in": date_list}}))

        for item in response:
            logging.info(f"item for clientId: {item['clientId']}, currencyCode: {item['currencyCode']}")
            bal_current_from_date = list(
                filter(
                    lambda x: x["client_id"] == item["clientId"]
                    and x["currency"] == item["currencyCode"]
                    and x["date"] == from_date,
                    balance_list,
                )
            )
            if bal_current_from_date:
                item["openingBalanceCurrent"] = bal_current_from_date[0]["opening_balance"]

                item["openingBalanceCurrentThousandth"] = bal_current_from_date[0]["opening_balance"] / 1000
            bal_current_to_date = list(
                filter(
                    lambda x: x["client_id"] == item["clientId"]
                    and x["currency"] == item["currencyCode"]
                    and x["date"] == current_to_date,
                    balance_list,
                )
            )
            if bal_current_to_date:
                item["closingBalanceCurrent"] = bal_current_to_date[0]["closing_balance"]
                item["closingBalanceCurrentThousandth"] = bal_current_to_date[0]["closing_balance"] / 1000

            bal_previous_from_date = list(
                filter(
                    lambda x: x["client_id"] == item["clientId"]
                    and x["currency"] == item["currencyCode"]
                    and x["date"] == previous_from_date,
                    balance_list,
                )
            )
            if bal_previous_from_date:
                item["openingBalancePrevious"] = bal_previous_from_date[0]["opening_balance"]
                item["openingBalancePreviousThousandth"] = bal_previous_from_date[0]["opening_balance"] / 1000
            bal_previous_to_date = list(
                filter(
                    lambda x: x["client_id"] == item["clientId"]
                    and x["currency"] == item["currencyCode"]
                    and x["date"] == previous_to_date,
                    balance_list,
                )
            )
            if bal_previous_to_date:
                item["closingBalancePrevious"] = bal_previous_to_date[0]["closing_balance"]
                item["closingBalancePreviousThousandth"] = bal_previous_to_date[0]["closing_balance"] / 1000

            item["openingBalanceComparison"] = (
                item["openingBalanceCurrent"] / item["openingBalancePrevious"]
                if item["openingBalancePrevious"] != 0
                else 0  # NaN
            )
            item["closingBalanceComparison"] = (
                item["closingBalanceCurrent"] / item["closingBalancePrevious"]
                if item["closingBalancePrevious"] != 0
                else 0  # NaN
            )
            basic_info = get_db().client_basic_info.find_one({"_id": item["clientId"]})
            item["clientId"] = basic_info["c_id"]
        logging.info("completed")
        return response

    def export_movements_of_funds(
        self,
        data_list,
        open_banking_data,
        name,
        client,
        from_date,
        to_date,
        currency,
        previous_from_date,
        previous_to_date,
    ):
        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        letter = list(string.ascii_uppercase)
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()
        worksheet.set_column("A:A", 30)
        col_list = [5, 10, 15, 20, 25]
        for i in range(1, 26):
            if i in col_list:
                worksheet.set_column(f"{letter[i]}:{letter[i]}", 3)
            else:
                worksheet.set_column(f"{letter[i]}:{letter[i]}", 12)

        merge_center = workbook.add_format({"bold": 1, "border": 1, "size": 11, "align": "center", "valign": "vcenter"})
        merge_center_1 = workbook.add_format({"bold": 1, "size": 11, "align": "center", "valign": "vcenter"})

        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        merge_left_1 = workbook.add_format({"size": 11, "align": "left"})

        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
        worksheet.write(
            "A1",
            f"Movements to and from the Trust Account for the period {change_month_format(from_date)[3:]} - {change_month_format(to_date)[3:]}",
            merge_left,
        )
        worksheet.merge_range(
            "A3:E3", f"{change_month_format(from_date)} - {change_month_format(to_date)}", merge_center
        )
        worksheet.merge_range(
            "G3:J3",
            f"Comparative {change_month_format(previous_from_date)} - {change_month_format(previous_to_date)}",
            merge_center,
        )
        worksheet.merge_range(
            "L3:O3", f"{change_month_format(from_date)} - {change_month_format(to_date)}", merge_center
        )
        worksheet.merge_range(
            "Q3:T3",
            f"Comparative {change_month_format(previous_from_date)} - {change_month_format(previous_to_date)}",
            merge_center,
        )
        worksheet.merge_range(
            "V3:Y3",
            "Comparative Percentage",
            merge_center,
        )
        row = 3
        col = 1
        header_dict = {
            "openingBalanceCurrent": "Opening Bal",
            "receiptsCurrent": "Receipts",
            "paymentsCurrent": "Payments",
            "closingBalanceCurrent": "Closing Bal",
            "openingBalancePrevious": "Opening Bal",
            "receiptsPrevious": "Receipts",
            "paymentsPrevious": "Payments",
            "closingBalancePrevious": "Closing Bal",
            "openingBalanceCurrentThousandth": "Opening Bal",
            "receiptsCurrentThousandth": "Receipts",
            "paymentsCurrentThousandth": "Payments",
            "closingBalanceCurrentThousandth": "Closing Bal",
            "openingBalancePreviousThousandth": "Opening Bal",
            "receiptsPreviousThousandth": "Receipts",
            "paymentsPreviousThousandth": "Payments",
            "closingBalancePreviousThousandth": "Closing Bal",
            "openingBalanceComparison": "Opening Bal",
            "receiptsComparison": "Receipts",
            "paymentsComparison": "Payments",
            "closingBalanceComparison": "Closing Bal",
        }

        head_dict = {}
        for key, value in header_dict.items():
            if col in col_list:
                col = col + 1
            worksheet.write(row, col, value, merge_left)
            head_dict[key] = col
            col += 1
        head_dict["clientName"] = 0

        for i in range(11, 20):
            if i == 15 or i == 20:
                continue
            worksheet.write(4, i, "'000", merge_center_1)

        row += 2
        open_banking_list = [open_banking_data]
        for data in open_banking_list:
            for key, value in data.items():
                if key not in head_dict.keys():
                    continue

                if key == "clientName":
                    worksheet.write(row, head_dict[key], f"{basic_info['full_name']}", merge_left_1)
                    continue

                worksheet.write(row, head_dict[key], value, amount_format)

            row += 1
        row += 1

        workbook.close()

    def banking_claim_summary_report(self, client, from_date, to_date, page, size):
        from_date = datetime.fromisoformat(from_date)
        to_date = datetime.fromisoformat(to_date)
        page = int(page or 1)
        size = int(size or 10)
        offset = (page - 1) * size

        client_match_condition = [{"client_id": ObjectId(client)}] if client else []
        data = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$addFields": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
                    {"$addFields": {"file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}}},
                    {
                        "$lookup": {
                            "from": "claims_file_details",
                            "localField": "recent_file.file_id",
                            "foreignField": "file_id",
                            "pipeline": [{"$match": {"deleted": False}}],
                            "as": "claim",
                        }
                    },
                    {"$unwind": "$claim"},
                    {"$addFields": {"element_type_lower": {"$toLower": "$claim.element"}}},
                    {
                        "$match": {
                            "$and": [
                                *client_match_condition,
                                {"file_datetime": {"$gte": from_date, "$lte": to_date}},
                                {"status": {"$nin": ["Cancelled"]}},
                            ]
                        }
                    },
                    {
                        "$group": {
                            "_id": {
                                "reportDate": "$file_datetime",
                                "currency": "$claim.currency_code",
                                "elementType": "$element_type_lower",
                            },
                            "amount": {"$sum": "$claim.amount"},
                        }
                    },
                    {
                        "$group": {
                            "_id": {"reportDate": "$_id.reportDate", "currency": "$_id.currency"},
                            "totalClaim": {"$sum": "$amount"},
                            "content": {"$push": {"elementType": "$_id.elementType", "amount": "$amount"}},
                        }
                    },
                    {
                        "$project": {
                            "paymentDate": [],
                            "totalClaim": 1,
                            "totalBanking": {"$literal": 0},
                            "elementType": "$content",
                            "fileType": "Claim File",
                            "currency": 1,
                        }
                    },
                    {
                        "$unionWith": {
                            "coll": "banking_metadata",
                            "pipeline": [
                                {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                                {
                                    "$addFields": {
                                        "file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}
                                    }
                                },
                                {
                                    "$lookup": {
                                        "from": "banking_file_details",
                                        "localField": "recent_file.file_id",
                                        "foreignField": "file_id",
                                        "pipeline": [{"$match": {"deleted": False}}],
                                        "as": "bank",
                                    }
                                },
                                {"$unwind": "$bank"},
                                {"$addFields": {"payment_type_lower": {"$toLower": "$bank.payment_type"}}},
                                {
                                    "$match": {
                                        "$and": [
                                            *client_match_condition,
                                            {"file_datetime": {"$gte": from_date, "$lte": to_date}},
                                            {"status": {"$nin": ["Cancelled"]}},
                                        ]
                                    },
                                },
                                {
                                    "$group": {
                                        "_id": {
                                            "reportDate": "$file_datetime",
                                            "currency": "$bank.currency_code",
                                            "paymentType": "$payment_type_lower",
                                        },
                                        "amount": {"$sum": "$bank.amount"},
                                        "paymentDate": {"$addToSet": "$bank.payment_date"},
                                    }
                                },
                                {
                                    "$group": {
                                        "_id": {"reportDate": "$_id.reportDate", "currency": "$_id.currency"},
                                        "totalBanking": {"$sum": "$amount"},
                                        "content": {"$push": {"paymentType": "$_id.paymentType", "amount": "$amount"}},
                                        "paymentDate": {"$addToSet": "$paymentDate"},
                                    }
                                },
                                {
                                    "$addFields": {
                                        "paymentDate": {
                                            "$reduce": {
                                                "input": "$paymentDate",
                                                "initialValue": [],
                                                "in": {"$setUnion": ["$$value", "$$this"]},
                                            }
                                        },
                                    }
                                },
                                {
                                    "$project": {
                                        "paymentDate": 1,
                                        "totalClaim": {"$literal": 0},
                                        "totalBanking": 1,
                                        "paymentType": "$content",
                                        "fileType": "banking File",
                                        "currency": 1,
                                    }
                                },
                            ],
                        }
                    },
                    {
                        "$group": {
                            "_id": "$_id",
                            "totalClaim": {"$sum": "$totalClaim"},
                            "totalBanking": {"$sum": "$totalBanking"},
                            "paymentDate": {"$addToSet": "$paymentDate"},
                            "paymentType": {"$push": "$paymentType"},
                            "elementType": {"$push": "$elementType"},
                        }
                    },
                    {
                        "$addFields": {
                            "paymentDate": {
                                "$reduce": {
                                    "input": "$paymentDate",
                                    "initialValue": [],
                                    "in": {"$setUnion": ["$$value", "$$this"]},
                                }
                            },
                            "paymentType": {
                                "$reduce": {
                                    "input": "$paymentType",
                                    "initialValue": [],
                                    "in": {"$setUnion": ["$$value", "$$this"]},
                                }
                            },
                            "elementType": {
                                "$reduce": {
                                    "input": "$elementType",
                                    "initialValue": [],
                                    "in": {"$setUnion": ["$$value", "$$this"]},
                                }
                            },
                        }
                    },
                    {"$sort": {"_id.reportDate": 1}},
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ]
            )
        )
        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        content = []
        for row in data[0]["data"]:
            row["totalClaim"] = row["totalClaim"] if row.get("totalClaim") else 0
            row["totalBanking"] = row["totalBanking"] if row.get("totalBanking") else 0
            currency = row.get("_id")["currency"]
            file_date = row.get("_id")["reportDate"]
            opening_closing_balance_details = get_db().opening_closing_balance.find_one(
                {"client_id": ObjectId(client), "currency": currency, "date": file_date}
            )
            if opening_closing_balance_details:
                opening_balance = opening_closing_balance_details.get("opening_balance")
                closing_balance = opening_closing_balance_details.get("closing_balance")
            else:
                opening_balance = 0
                closing_balance = 0
            cheque = extract_amount("cheque", "paymentType", row.get("paymentType"))
            bank_transfer = extract_amount("bank transfer", "paymentType", row.get("paymentType"))
            cash = extract_amount("cash", "paymentType", row.get("paymentType"))
            paysafe_amount = extract_amount("paysafe amount", "paymentType", row.get("paymentType"))
            card = extract_amount("card", "paymentType", row.get("paymentType"))
            other_payment_amount = (row["totalBanking"]) - (cheque + bank_transfer + cash + paysafe_amount + card)

            performance = extract_amount("performance", "elementType", row.get("elementType"))
            commission = extract_amount("commission", "elementType", row.get("elementType"))
            cancellation = extract_amount("cancellation", "elementType", row.get("elementType"))
            refund = extract_amount("refund", "elementType", row.get("elementType"))
            non_trust = extract_amount("non-trust", "elementType", row.get("elementType"))
            lcf = extract_amount("lcf", "elementType", row.get("elementType"))
            charter_flights_or_scheduled_flights = extract_amount(
                "charter flights/scheduled flights", "elementType", row.get("elementType")
            )
            bsp = extract_amount("bsp", "elementType", row.get("elementType"))
            atol = extract_amount("atol", "elementType", row.get("elementType"))
            crop_card = extract_amount("crop card", "elementType", row.get("elementType"))
            apc_fee = extract_amount("apc fee", "elementType", row.get("elementType"))
            balance = extract_amount("balance", "elementType", row.get("elementType"))
            deposit = extract_amount("deposit", "elementType", row.get("elementType"))
            cruise = extract_amount("cruise", "elementType", row.get("elementType"))
            insurance = extract_amount("insurance", "elementType", row.get("elementType"))
            other_element_amount = (row["totalClaim"]) - (
                performance
                + commission
                + cancellation
                + refund
                + non_trust
                + lcf
                + charter_flights_or_scheduled_flights
                + bsp
                + atol
                + crop_card
                + apc_fee
                + balance
                + deposit
                + cruise
                + insurance
            )
            payment_dates = sorted([payment_date for payment_date in row["paymentDate"] if payment_date is not None])
            file_dict = {
                "clientId": basic_info["c_id"],
                "clientName": basic_info["full_name"],
                "friendlyName": basic_info["friendly_name"],
                "reportDate": datetime.strftime(row.get("_id")["reportDate"], "%d/%m/%Y"),
                "paymentDate": payment_dates,
                "openingBalance": opening_balance,
                "totalBanking": row["totalBanking"],
                "cheque": cheque,
                "bankTransfer": bank_transfer,
                "cash": cash,
                "paysafeAmount": paysafe_amount,
                "card": card,
                "otherPaymentAmount": other_payment_amount,
                "performance": performance,
                "commission": commission,
                "cancellation": cancellation,
                "refund": refund,
                "nonTrust": non_trust,
                "lcf": lcf,
                "charterFlightsOrScheduledFlights": charter_flights_or_scheduled_flights,
                "bsp": bsp,
                "atol": atol,
                "cropCard": crop_card,
                "apcFee": apc_fee,
                "balance": balance,
                "deposit": deposit,
                "cruise": cruise,
                "insurance": insurance,
                "otherElementAmount": other_element_amount,
                "totalClaim": row["totalClaim"],
                "closingBalance": closing_balance,
                "currency": row.get("_id")["currency"],
            }
            content.append(file_dict)

        empty = True if not content else False
        first = True if page == 1 else False
        last = True if data[0]["metadata"] and page == ceil((data[0]["metadata"][0]["total"]) / size) else False
        total_elements = 0
        total_pages = 0
        if data[0]["metadata"]:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def export_banking_claim_summary_report(self, header_dict, data_list, name):
        amount_list = [
            "openingBalance",
            "totalBanking",
            "cheque",
            "bankTransfer",
            "cash",
            "paysafeAmount",
            "card",
            "otherPaymentAmount",
            "totalClaim",
            "closingBalance",
            "performance",
            "bankCharges",
            "commission",
            "cancellation",
            "refund",
            "nonTrust",
            "lcf",
            "charterFlightsOrScheduledFlights",
            "bsp",
            "atol",
            "cropCard",
            "apcFee",
            "balance",
            "deposit",
            "cruise",
            "insurance",
            "otherElementAmount",
            "totalClaim",
            "closingBalance",
        ]
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()

        merge_center = workbook.add_format({"bold": 1, "border": 1, "size": 11, "align": "center", "valign": "vcenter"})
        merge_right = workbook.add_format({"size": 11, "align": "right"})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right"})
        cell_format = workbook.add_format({"bold": 1, "border": 1, "size": 11, "align": "center"})
        cell_format.set_text_wrap()

        worksheet.merge_range("A1:P1", "Banking", merge_center)
        worksheet.merge_range("Q1:AB1", "Claims", merge_center)

        row = 1
        col = 0
        head_dict = {}
        for key, value in header_dict.items():
            worksheet.write(row, col, value, cell_format)
            head_dict[key] = col
            col += 1
        row = 2
        for data in data_list:
            for key, value in data.items():
                if key not in head_dict.keys():
                    continue
                if key in amount_list:
                    worksheet.write(row, head_dict[key], value, amount_format)
                else:
                    worksheet.write(row, head_dict[key], value, merge_right)
            row += 1
        workbook.close()

    def compliance_computation_report(self, client, currency, from_date, to_date):
        client_data = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        if not client_data:
            raise ServiceException("client not found")
        banking_claim_amount = self.banking_and_claim_between_dates(client, currency, from_date, to_date, True)
        previous_date = datetime.strptime(from_date, "%Y-%m-%d") - timedelta(days=1)
        previous_date_str = previous_date.strftime("%Y-%m-%d")
        start_date = "2020-01-01"
        banking_claim_amount_previous = self.banking_and_claim_between_dates(
            client, currency, start_date, previous_date_str, True
        )
        banking_amount_previous = banking_claim_amount_previous["bankingAmount"]
        claim_amount_previous = banking_claim_amount_previous["claimAmount"]
        closing_balance_previous = banking_amount_previous - claim_amount_previous
        new_closing_balance_current = (
            closing_balance_previous + banking_claim_amount["bankingAmount"] - banking_claim_amount["claimAmount"]
        )
        response = {
            "closingBalancePrevious": round(closing_balance_previous, 2),
            "bankingAmount": round(banking_claim_amount["bankingAmount"], 2),
            "claimAmount": -round((banking_claim_amount["claimAmount"]), 2)
            if banking_claim_amount["claimAmount"] != 0
            else 0.0,
            "closingBalanceCurrent": round(new_closing_balance_current, 2),
            "amount": round(new_closing_balance_current, 2),
            "clientName": client_data["friendly_name"],
        }
        return response

    def compliance_computation_report_wlh(self, client, currency, from_date, to_date):
        client_data = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        if not client_data:
            raise ServiceException("client not found")
        from_date = datetime.fromisoformat(from_date)
        to_date = datetime.fromisoformat(to_date)
        to_date = datetime.combine(to_date, datetime.max.time())

        data = get_db().banking_metadata.aggregate(
            [
                {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                {"$addFields": {"file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}}},
                {
                    "$match": {
                        "$and": [
                            {"client_id": ObjectId(client)},
                            {"file_datetime": {"$lte": to_date}},
                            {"status": {"$nin": ["Cancelled", "Cancelled by System"]}},
                        ]
                    }
                },
                {"$sort": {"file_datetime": -1}},
                {"$limit": 3},
                {
                    "$lookup": {
                        "from": "banking_file_details",
                        "localField": "recent_file.file_id",
                        "foreignField": "file_id",
                        "pipeline": [
                            {"$match": {"currency_code": currency, "deleted": False}},
                            {
                                "$group": {
                                    "_id": None,
                                    "balance": {"$sum": "$balance"},
                                    "original_balance": {"$sum": "$original_balance"},
                                }
                            },
                        ],
                        "as": "bank",
                    }
                },
                {"$unwind": "$bank"},
                {
                    "$project": {
                        "_id": 1,
                        "balance_total": "$bank.balance",
                        "original_balance_total": "$bank.original_balance",
                        "file_date": "$recent_file.file_date",
                        "file_name": "$recent_file.file_name",
                        "file_datetime": 1,
                        "net_amount": "$recent_file.net_amount",
                    }
                },
            ],
        )
        file1 = next(data, None)
        file2 = next(data, None)
        file3 = next(data, None)
        closing_balance_previous = file3["balance_total"] if file3 else 0
        if client == current_app.config["WLH_NEW"]:
            funds_in = 0
            funds_out = 0
            net_amount = file1.get("net_amount")["GBP"] if file1 else 0
            agreed_funds = net_amount
            if file2:
                closing_balance_current = file2["net_amount"]["GBP"] if file2.get("net_amount") else 0
            else:
                closing_balance_current = 0
            if file3:
                wlh_claim_amount = file3["net_amount"]["GBP"] if file3.get("net_amount") else 0
            else:
                wlh_claim_amount = 0

            funds_paid = closing_balance_current - wlh_claim_amount
            if funds_paid > 0:
                funds_in = funds_paid
            if funds_paid < 0:
                funds_out = funds_paid
        else:
            closing_balance_current = file2["balance_total"] if file2 else 0
        if closing_balance_current > closing_balance_previous:
            banking_amount = closing_balance_current - closing_balance_previous
            claim_amount = 0
        else:
            banking_amount = 0
            claim_amount = closing_balance_current - closing_balance_previous
        if client == current_app.config["WLH_NEW"]:
            claim_amount = wlh_claim_amount

        response = {
            "originalAmount": file1["original_balance_total"] if file1 else 0,
            "closingBalancePrevious": closing_balance_previous,
            "bankingAmount": banking_amount,
            "claimAmount": claim_amount,
            "closingBalanceCurrent": closing_balance_current,
            "amount": file1["balance_total"] if file1 else 0,
            "clientName": client_data["friendly_name"],
        }

        if client == current_app.config["WLH_NEW"]:
            response.update({"agreedFunds": agreed_funds, "fundsIn": funds_in, "fundsOut": funds_out})

        header_list = []
        if file2:
            first_date = file2["file_datetime"].replace(day=1)
            multiplier = get_db().client_escrow_multiplier.find_one(
                {"client_id": ObjectId(client), "date": first_date.strftime("%Y-%m-%d")}
            )
            if not multiplier:
                multiplier = {"multiplier": 0.7, "credit_multiplier": None, "debit_multiplier": None}
            header_data = (
                f'Filename: {file2["file_name"]} File Date: {file2["file_date"]}'
                if client == current_app.config["WLH_NEW"]
                else f'Filename: {file2["file_name"]} File Date: {file2["file_date"]} Prev. MALT: 0.00  GAR PCT: {multiplier.get("multiplier")}'
            )
            header_list.append(header_data)
        if file1:
            first_date = file1["file_datetime"].replace(day=1)
            multiplier = get_db().client_escrow_multiplier.find_one(
                {"client_id": ObjectId(client), "date": first_date.strftime("%Y-%m-%d")}
            )
            if not multiplier:
                multiplier = {"multiplier": 0.7, "credit_multiplier": None, "debit_multiplier": None}
            header_data = (
                f'Filename: {file1["file_name"]} File Date: {file1["file_date"]}'
                if client == current_app.config["WLH_NEW"]
                else f'Filename: {file1["file_name"]} File Date: {file1["file_date"]} Curr. MALT: 0.00  GAR PCT: {multiplier.get("multiplier")} %DCC: {multiplier.get("credit_multiplier")} %DDD: {multiplier.get("debit_multiplier")}'
            )
            header_list.append(header_data)

        response.update({"header": header_list})
        return response

    def export_compliance_computation_report(self, data, name, client):
        workbook = open(f"{current_app.config['TEMP_DIR']}/{name}", "w")
        if data.get("header"):
            header_data = "\n".join(data["header"])
            workbook.write(header_data)
            workbook.write("\n\n")
        if client == current_app.config["WLH_NEW"]:

            workbook.write("Compliance Entry\n\n")
            workbook.write(
                f'(a) £ {data["claimAmount"]:,.2f} was standing to the credit of the Trustee Account on the Prior Compliance Reporting Day \n'
            )
            workbook.write(
                f'(b) £ {data["fundsIn"]:,.2f} has been paid into the Trustee Account since the Prior Compliance Reporting Day\n'
            )
            workbook.write(
                f'(c) £ {data["fundsOut"]:,.2f} has been paid by the Trustee in response to Payment Requests since the Prior Compliance Reporting Day\n'
            )
            workbook.write(
                f'(d) The Total Consumer Funds (as defined below) standing to the credit of the Trustee Account is an amount equal to £ {data["closingBalanceCurrent"]:,.2f}\n'
            )
            workbook.write(
                f'(e) The Agreed Protected Funds for the month following the Relevant Compliance Reporting Day is an amount equal to £ {data["agreedFunds"]:,.2f}\n\n'
            )
            workbook.write("Note: We confirm that no Bond Reduction Notices have been received.")

        else:
            workbook.write("Compliance Entry\n\n")
            workbook.write(
                f'(a) £ {data["closingBalancePrevious"]:,.2f} was standing to the credit of the Trustee Account on the Prior Compliance Reporting Day \n'
            )
            workbook.write(
                f'(b) £ {data["bankingAmount"]:,.2f} has been paid into the Trustee Account since the Prior Compliance Reporting Day\n'
            )
            workbook.write(
                f'(c) £ {data["claimAmount"]:,.2f} has been paid by the Trustee in response to Payment Requests since the Prior Compliance Reporting Day\n'
            )
            workbook.write(
                f'(d) The Total Consumer Funds standing to the credit of the Trustee Account is an amount equal to £ {data["closingBalanceCurrent"]:,.2f}\n'
            )
            workbook.write(f'(e) The Agreed Protected Funds  is an amount equal to £ {data["amount"]:,.2f}\n\n')
            workbook.write("Note: We confirm that no Bond Reduction Notices have been received.")
        workbook.close()

    def report_files(self, data):
        report_file = get_db().report_files.find_one_or_404(ReportFilesSchema().load(data))
        return ReportFilesSchema().dump(report_file)

    def atol_renewal_tracker_list(self, client):
        client_data = get_db().client_basic_info.find_one(
            {"_id": ObjectId(client)},
            projection={"friendly_name": 1},
        )
        if not client_data:
            raise ServiceException("client not found")
        atol_info_list = list(
            get_db().client_atol_info.find(
                {"client_id": ObjectId(client)},
                projection={"_id": 0, "license": 1, "start_date": 1, "expiry_date": 1},
            )
        )
        response = [ClientAtolInfoSchema().dump(client_atol_info) for client_atol_info in atol_info_list]
        for item in response:
            item.update(
                {
                    "clientName": client_data["friendly_name"],
                }
            )
        return response

    def insurance_renewal_tracker_list(self, client):
        client_id = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        if not client_id:
            raise ServiceException("client not found")
        data = list(
            get_db().client_insurance_info.aggregate(
                [
                    {"$match": {"client_id": ObjectId(client)}},
                    {
                        "$lookup": {
                            "from": "client_files",
                            "localField": "supplier_list_file.file_id",
                            "foreignField": "file_id",
                            "as": "client_file",
                        }
                    },
                    {"$unwind": {"path": "$client_file", "preserveNullAndEmptyArrays": True}},
                    {
                        "$project": {
                            "clientId": "$_id",
                            "clientName": "$friendly_name",
                            "policyNo": "$policy_no",
                            "provider": "$provider",
                            "expiryDate": {"$dateToString": {"format": "%Y-%m-%d", "date": "$expiry_date"}},
                            "supplier_list": "$client_file.supplier_list",
                            "totalMaxCap": {"$sum": "$client_file.supplier_list.cap_amount"},
                        }
                    },
                ]
            )
        )
        new_list = []

        for item in data:
            response = {
                "clientName": client_id["friendly_name"],
                "policyNumber": item["policyNo"],
                "provider": item["provider"],
                "expiryDate": item["expiryDate"],
                "supplierName": None,
                "capAmount": None,
                "totalMaxCap": item.get("totalMaxCap"),
            }
            if item.get("supplier_list"):
                response["supplierName"] = item["supplier_list"][0].get("supplier_name")
                response["capAmount"] = item["supplier_list"][0].get("cap_amount")
                new_list.append(response)
                for supplier in item["supplier_list"][1:]:
                    new_list.append(
                        {
                            "clientName": None,
                            "policyNumber": None,
                            "provider": None,
                            "expiryDate": None,
                            "supplierName": supplier.get("supplier_name"),
                            "capAmount": supplier.get("cap_amount"),
                            "totalMaxCap": None,
                        }
                    )
            else:
                new_list.append(response)
        return new_list

    def daily_agent_exceptions_report(self, data):
        client = data.get("client")
        currency = data.get("currency")
        page = int(data.get("page") or 1)
        size = int(data.get("size") or 10)
        offset = (page - 1) * size
        collection_name = "trust_fund" if client == os.environ.get("NAS") else "trust_fund_v2"
        date_today = datetime.today().strftime("%Y-%m-%d")
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        lead_time = client_basic_info.get("lead_time")
        if lead_time is None:
            response = make_response(jsonify({"message": "Lead Time is not set for the selected client"}))
            response.status_code = 409
            abort(response)
        client_and_currency_match_condition = [{"$match": {"$and": []}}] if client or currency else []
        if client:
            client_and_currency_match_condition[0]["$match"]["$and"].append({"client_id": ObjectId(client)})
        if currency:
            client_and_currency_match_condition[0]["$match"]["$and"].append({"currency_code": {"$all": [currency]}})
        data = list(
            get_db()[collection_name].aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                {
                                    "departure_date": {"$exists": "true"},
                                    "$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}],
                                }
                            ]
                        }
                    },
                    {
                        "$addFields": {
                            "days": {
                                "$dateDiff": {
                                    "startDate": {"$toDate": date_today},
                                    "endDate": {"$toDate": "$departure_date"},
                                    "unit": "day",
                                }
                            }
                        }
                    },
                    {"$match": {"days": {"$gt": lead_time}}},
                    *client_and_currency_match_condition,
                    {"$sort": {"balance": 1}},
                    {
                        "$lookup": {
                            "from": "banking_file_details",
                            "let": {"current_client_id": "$client_id"},
                            "localField": "booking_ref",
                            "foreignField": "booking_ref",
                            "pipeline": [
                                {
                                    "$match": {
                                        "$expr": {
                                            "$and": [
                                                {"$eq": ["$client_id", "$$current_client_id"]},
                                                {"$eq": ["$deleted", False]},
                                                {"$eq": ["$type", "agent"]},
                                            ]
                                        }
                                    }
                                },
                                {"$sort": {"updated_at": 1}},
                                {
                                    "$group": {
                                        "_id": {"booking_ref": "$booking_ref"},
                                        "refund": {"$sum": {"$cond": [{"$lt": ["$amount", 0]}, "$amount", 0]}},
                                        "bonding": {"$last": "$bonding"},
                                    }
                                },
                            ],
                            "as": "bank",
                        }
                    },
                    {"$unwind": "$bank"},
                    {
                        "$project": {
                            "_id": "$_id",
                            "bookingRef": "$booking_ref",
                            "leadPax": "$lead_pax",
                            "paxCount": "$pax_count",
                            "departureDate": "$departure_date",
                            "returnDate": "$return_date",
                            "bookingDate": "$booking_date",
                            "totalBanked": {"$ifNull": ["$total_in_trust", 0]},
                            "totalClaimed": {"$ifNull": ["$total_claimed", 0]},
                            "totalBookingValue": "$total_booking_value",
                            "refund": {"$ifNull": ["$bank.refund", 0]},
                            "balance": "$balance",
                            "bonding": "$bank.bonding",
                            "leadTime": "$days",
                        }
                    },
                    {
                        "$facet": {
                            "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                            "data": [{"$skip": offset}, {"$limit": size}],
                        }
                    },
                ],
                collation={"locale": "en", "strength": 1},
                allowDiskUse=True,
            )
        )
        content = []
        for row in data[0]["data"]:
            refunds = row["refund"]
            banking_dict = {
                "_id": str(row["_id"]),
                "bookingRef": row["bookingRef"],
                "totalBookingValue": row.get("totalBookingValue"),
                "leadPax": row.get("leadPax"),
                "numberOfPax": row.get("paxCount"),
                "bookingDate": row.get("bookingDate"),
                "dateOfTravel": row.get("departureDate"),
                "dateOfReturn": row.get("returnDate"),
                "deposits": row.get("totalBanked") + abs(refunds),
                "leadTime": row.get("leadTime"),
                "totalInTrust": row.get("totalBanked"),
                "totalClaimAmount": row.get("totalClaimed"),
                "totalAgentAmountInTrust": row.get("balance"),
                "bondType": row.get("bonding"),
                "clientName": client_basic_info["friendly_name"],
                "clientId": str(client_basic_info["_id"]),
            }
            content.append(banking_dict)
        total_elements = 0
        total_pages = 0
        if content:
            total_elements = data[0]["metadata"][0]["total"]
            total_pages = ceil((data[0]["metadata"][0]["total"]) / size)
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil(((total_elements) / size)) else False
        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def tbr_sftp_upload(self, query):
        file_type = query["type"]
        client_id = query["client"]
        file_id = query["fileId"]

        reports_bucket = current_app.config["REPORTS_BUCKET"]
        # sftp_bucket = current_app.config["SFTP_BUCKET"]
        sftp_bucket = current_app.config["PTT_BUCKET"]
        download_file(reports_bucket, file_id, f"{current_app.config['TEMP_DIR']}/{file_id}")

        client_details = get_db().client_basic_info.find_one(
            {"_id": ObjectId(client_id)}, projection={"_id": 0, "sftp_location_reports": 1, "full_name": 1}
        )
        sftp_location_reports = client_details.get("sftp_location_reports")
        full_name = client_details["full_name"]
        if not sftp_location_reports:
            response = make_response(jsonify({"message": "SFTP Reports Folder location is not set for this client"}))
            response.status_code = 409
            abort(response)
        upload_location = f"{sftp_location_reports[1:]}Trust-balance-report_{full_name}.{file_type}"
        upload_file(bucket=sftp_bucket, key=upload_location, file_path=f"{current_app.config['TEMP_DIR']}/{file_id}")

    def export_hsbc_bank_reconciliation_statement_report(
        self, data, open_banking_data, hide_warnings, name, client, from_date, to_date, currency
    ):
        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        from_date = format_date_with_day_ordinal(from_date)
        to_date = format_date_with_day_ordinal(to_date)
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()
        worksheet.set_column("A:A", 60)
        worksheet.set_column("B:B", 20)
        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        merge_center = workbook.add_format({"bold": 1, "size": 11, "align": "center"})
        merge_left_1 = workbook.add_format({"size": 11, "align": "left"})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
        amount_format_red = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "color": "red"}
        )
        cell_format = workbook.add_format()
        cell_format.set_text_wrap()

        warning_format = workbook.add_format()
        warning_format.set_text_wrap()
        warning_format.set_font_color("red")

        closing_balance_at_format = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "bottom": 6, "top": 1}
        )

        worksheet.write("A1", f"{basic_info['full_name']}", merge_left)
        worksheet.write("A2", "Statement of Trust Account", merge_left)
        worksheet.write("A3", f"For the Month Ending {to_date}", merge_left)
        worksheet.write("B3", "Trust Account", merge_center)
        worksheet.write("B4", get_currency_symbol(currency), merge_center)

        line_no = 8
        counter = 2

        if open_banking_data:
            if open_banking_data.get("opening_balance"):
                worksheet.write(
                    "A6",
                    f"Opening Balance as at {from_date}",
                    cell_format,
                )
                worksheet.write("B6", open_banking_data.get("opening_balance"), amount_format)
            if open_banking_data.get("trust_non_trust_payment_received"):
                worksheet.write(
                    "A8",
                    "Trust and Non-trust Payment received",
                    cell_format,
                )
                worksheet.write("B8", open_banking_data["trust_non_trust_payment_received"], amount_format)
                line_no = line_no + counter
            if open_banking_data.get("payment_released"):
                worksheet.write(
                    f"A{line_no}",
                    "Payment Released",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data["payment_released"], amount_format)
                line_no = line_no + counter
            if open_banking_data.get("trust_funds_sent_to_ptt"):
                worksheet.write(
                    f"A{line_no}",
                    "Trust Funds sent to PTT",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data.get("trust_funds_sent_to_ptt"), amount_format)
                line_no = line_no + counter
            if open_banking_data.get("unreconciled_transactions"):
                worksheet.write(
                    f"A{line_no}",
                    "Unreconciled Transactions",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data.get("unreconciled_transactions"), amount_format)
                line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                f"Closing Balance as at {to_date}",
                cell_format,
            )
            worksheet.write(f"B{line_no}", open_banking_data["closing_balance"], closing_balance_at_format)
            line_no = line_no + counter
            if open_banking_data.get("closing_balance_on_statement"):
                worksheet.write(
                    f"A{line_no}",
                    "Closing Balance as per bank statement",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data["closing_balance_on_statement"], amount_format)
                line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                "Difference",
                cell_format,
            )
            worksheet.write(f"B{line_no}", open_banking_data["difference"], amount_format)
            line_no = line_no + counter

            worksheet.write(f"A{line_no}", data["accountingStatement"], cell_format)
            worksheet.write(
                f"A{line_no + 2}",
                f"{basic_info['full_name']} certify this statement as being true and accurate",
                merge_left_1,
            )
            worksheet.write(f"A{line_no + 4}", "Signed", merge_left_1)
            worksheet.write(f"A{line_no + 8}", "Position", merge_left_1)
            worksheet.write(f"A{line_no + 12}", "Date", merge_left_1)

        workbook.close()

    def export_hsbc_bank_reconciliation_statement_report(
        self, data, open_banking_data, hide_warnings, name, client, from_date, to_date, currency
    ):
        basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        from_date = format_date_with_day_ordinal(from_date)
        to_date = format_date_with_day_ordinal(to_date)
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()
        worksheet.set_column("A:A", 60)
        worksheet.set_column("B:B", 20)
        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        merge_center = workbook.add_format({"bold": 1, "size": 11, "align": "center"})
        merge_left_1 = workbook.add_format({"size": 11, "align": "left"})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
        amount_format_red = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "color": "red"}
        )
        cell_format = workbook.add_format()
        cell_format.set_text_wrap()

        warning_format = workbook.add_format()
        warning_format.set_text_wrap()
        warning_format.set_font_color("red")

        closing_balance_at_format = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "bottom": 6, "top": 1}
        )

        worksheet.write("A1", f"{basic_info['full_name']}", merge_left)
        worksheet.write("A2", "Statement of Trust Account", merge_left)
        worksheet.write("A3", f"For the Month Ending {to_date}", merge_left)
        worksheet.write("B3", "Trust Account", merge_center)
        worksheet.write("B4", get_currency_symbol(currency), merge_center)

        line_no = 8
        counter = 2

        if open_banking_data:
            if open_banking_data.get("opening_balance"):
                worksheet.write(
                    "A6",
                    f"Opening Balance as at {from_date}",
                    cell_format,
                )
                worksheet.write("B6", open_banking_data.get("opening_balance"), amount_format)
            if open_banking_data.get("trust_non_trust_payment_received"):
                worksheet.write(
                    "A8",
                    "Trust and Non-trust Payment received",
                    cell_format,
                )
                worksheet.write("B8", open_banking_data["trust_non_trust_payment_received"], amount_format)
                line_no = line_no + counter
            if open_banking_data.get("payment_released"):
                worksheet.write(
                    f"A{line_no}",
                    "Payment Released",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data["payment_released"], amount_format)
                line_no = line_no + counter
            if open_banking_data.get("trust_funds_sent_to_ptt"):
                worksheet.write(
                    f"A{line_no}",
                    "Trust Funds sent to PTT",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data.get("trust_funds_sent_to_ptt"), amount_format)
                line_no = line_no + counter
            if open_banking_data.get("unreconciled_transactions"):
                worksheet.write(
                    f"A{line_no}",
                    "Unreconciled Transactions",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data.get("unreconciled_transactions"), amount_format)
                line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                f"Closing Balance as at {to_date}",
                cell_format,
            )
            worksheet.write(f"B{line_no}", open_banking_data["closing_balance"], closing_balance_at_format)
            line_no = line_no + counter
            if open_banking_data.get("closing_balance_on_statement"):
                worksheet.write(
                    f"A{line_no}",
                    "Closing Balance as per bank statement",
                    cell_format,
                )
                worksheet.write(f"B{line_no}", open_banking_data["closing_balance_on_statement"], amount_format)
                line_no = line_no + counter
            worksheet.write(
                f"A{line_no}",
                "Difference",
                cell_format,
            )
            worksheet.write(f"B{line_no}", open_banking_data["difference"], amount_format)
            line_no = line_no + counter

            worksheet.write(f"A{line_no}", data["accountingStatement"], cell_format)
            worksheet.write(
                f"A{line_no + 2}",
                f"{basic_info['full_name']} certify this statement as being true and accurate",
                merge_left_1,
            )
            worksheet.write(f"A{line_no + 4}", "Signed", merge_left_1)
            worksheet.write(f"A{line_no + 8}", "Position", merge_left_1)
            worksheet.write(f"A{line_no + 12}", "Date", merge_left_1)

        workbook.close()

    def fetch_non_trust_bookings_data(self, client_id, currency, from_date, to_date, page, size):
        if from_date:
            from_date = datetime.fromisoformat(from_date)
        else:
            from_date = datetime.min
        if to_date:
            to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())
        else:
            to_date = datetime.max

        offset = (page - 1) * size

        pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "status": {"$nin": ["Cancelled", "Cancelled by System"]},
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                }
            },
            {"$unwind": "$banking_files"},
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "localField": "banking_files.file_id",
                    "foreignField": "file_id",
                    "as": "file_details",
                }
            },
            {"$unwind": "$file_details"},
            {"$match": {"file_details.non_trust_flag": True}},
            {"$replaceRoot": {"newRoot": "$file_details"}},
            {
                "$facet": {
                    "metadata": [{"$count": "total"}, {"$addFields": {"page": page}}],
                    "data": [{"$skip": offset}, {"$limit": size}],
                }
            },
        ]

        result = list(get_db().banking_metadata.aggregate(pipeline))
        content = result[0]["data"]
        for item in content:
            item.pop("_id", None)
            item.pop("client_id", None)
            item.pop("file_id", None)
            item.pop("banking_id", None)
        total_elements = result[0]["metadata"][0]["total"] if result[0]["metadata"] else 0
        total_pages = ceil(total_elements / size) if total_elements > 0 else 0
        empty = True if not content else False
        first = True if page == 1 else False
        last = True if page == ceil((total_elements) / size) else False

        response = {
            "content": content,
            "empty": empty,
            "first": first,
            "last": last,
            "pageNumber": page,
            "numberOfElements": len(content),
            "totalElements": total_elements,
            "totalPages": total_pages,
        }
        return response

    def fetch_non_trust_bookings(self, client_id, currency, from_date, to_date):
        if from_date:
            from_date = datetime.fromisoformat(from_date)
        else:
            from_date = datetime.min
        if to_date:
            to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())
        else:
            to_date = datetime.max

        pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "status": {"$nin": ["Cancelled", "Cancelled by System"]},
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                }
            },
            {"$unwind": "$banking_files"},
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "localField": "banking_files.file_id",
                    "foreignField": "file_id",
                    "as": "file_details",
                }
            },
            {"$unwind": "$file_details"},
            {"$match": {"file_details.non_trust_flag": True}},
            {"$replaceRoot": {"newRoot": "$file_details"}},
        ]

        response = list(get_db().banking_metadata.aggregate(pipeline))
        return response

    def fetch_forty_percent_data(self, client_id, from_date, to_date):
        from_date = datetime.strptime(from_date, "%Y-%m-%d")
        to_date = datetime.strptime(to_date, "%Y-%m-%d")
        from_month = from_date.strftime("%Y-%m")
        to_month = to_date.strftime("%Y-%m")

        target_month = to_month
        if from_month == to_month:
            target_month = from_month
        sent_to_trust = 0
        from_trust = 0

        metadata_pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "status": {"$nin": ["Authorised", "Cancelled", "Cancelled by System"]},
                }
            },
            {"$unwind": "$banking_files"},
            {
                "$match": {
                    "banking_files.status": {"$nin": ["Cancelled", "Cancelled by System"]},
                }
            },
            {
                "$addFields": {
                    "banking_files.file_date": {
                        "$dateFromString": {"dateString": "$banking_files.file_date", "format": "%Y-%m-%d"}
                    }
                }
            },
            {"$match": {"banking_files.file_date": {"$gte": from_date, "$lte": to_date}}},
            {"$group": {"_id": None, "total_deposit": {"$sum": "$banking_files.deposit.CZK"}}},
        ]

        deductable_pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "date": {"$regex": f"^{target_month}"},
                }
            },
            {"$group": {"_id": None, "total_deductable": {"$sum": "$deductable_amount"}}},
        ]
        claims_metadata_pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "status": {"$nin": ["Authorised", "Cancelled", "Cancelled by System"]},
                }
            },
            {"$unwind": "$claim_files"},
            {
                "$match": {
                    "claim_files.status": {"$nin": ["Cancelled", "Cancelled by System"]},
                }
            },
            {
                "$addFields": {
                    "claim_files.file_date": {
                        "$dateFromString": {"dateString": "$claim_files.file_date", "format": "%Y-%m-%d"}
                    }
                }
            },
            {"$match": {"claim_files.file_date": {"$gte": from_date, "$lte": to_date}}},
            {"$group": {"_id": None, "total_claims": {"$sum": "$claim_files.claim_total.CZK"}}},
        ]

        result = list(get_db().banking_metadata.aggregate(metadata_pipeline))
        total_deposit = result[0]["total_deposit"] if result else 0

        deductable_result = list(get_db().client_deductable_amount.aggregate(deductable_pipeline))
        total_deductable = deductable_result[0]["total_deductable"] if deductable_result else 0

        claim_result = list(get_db().claims_metadata.aggregate(claims_metadata_pipeline))
        total_claims = claim_result[0]["total_claims"] if claim_result else 0

        if total_deposit > total_deductable:
            y = total_deposit
        else:
            y = total_deductable

        rebalancing_protected_advances = y - total_claims

        if rebalancing_protected_advances > 0:
            sent_to_trust = rebalancing_protected_advances
        else:
            from_trust = rebalancing_protected_advances

        return jsonify(
            {
                "total_deposit": total_deposit,
                "total_deductable": total_deductable,
                "total_claims": total_claims,
                "rebalancing_protected_advances": rebalancing_protected_advances,
                "sent_to_trust": sent_to_trust,
                "from_trust": from_trust,
            }
        )

    def export_forty_percent_data(self, data, from_date, to_date, file_name, balance):
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")
        worksheet = workbook.add_worksheet("Rebalancing Report")

        from_date = change_date_format(from_date)
        numeric_from_date = format_date_from_numeric(from_date)
        to_date = change_date_format(to_date)
        from_date = format_date_with_day_ordinal(from_date)
        to_date = format_date_with_day_ordinal(to_date)

        worksheet.set_column("A:A", 60)
        worksheet.set_column("B:B", 30)

        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        merge_center = workbook.add_format({"bold": 1, "size": 11, "align": "center"})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
        cell_format = workbook.add_format()
        bold_underline = workbook.add_format({"bold": True, "underline": True, "size": 11, "align": "left"})

        cell_format.set_text_wrap()
        warning_format = workbook.add_format()
        warning_format.set_text_wrap()
        warning_format.set_font_color("red")

        worksheet.write("A1", "ACT Azure Trust (for Blue Style a.s.)", merge_left)
        worksheet.write("A2", "Consolidated Statement of Trust Accounts", merge_left)
        worksheet.write("A3", f"For the Week Ending {to_date}", merge_left)

        worksheet.write("A6", "Computation", merge_left)
        worksheet.write("B6", "Czech Crowns", merge_center)

        line_no = 10
        counter = 2

        worksheet.write(
            "A8",
            "40% of all Advances in respect of Tours which are received one day or more prior to the commencement of the Tour ",
            cell_format,
        )
        worksheet.write(
            "B8",
            data["total_deposit"],
            amount_format,
        )
        worksheet.write(
            "A10",
            f"Deductables as per Insurance contract for {numeric_from_date}",
            cell_format,
        )
        worksheet.write(
            "B10",
            data["total_deductable"],
            amount_format,
        )
        line_no = line_no + counter
        worksheet.write(
            f"A{line_no}",
            "Balance as per ACT Azure  Trust Bank Accounts",
            cell_format,
        )
        worksheet.write(
            f"B{line_no}",
            balance,
            amount_format,
        )
        line_no = line_no + counter
        if data["sent_to_trust"]:
            worksheet.write(
                f"A{line_no}",
                "Advances to be Sent to trust",
                bold_underline,
            )
            line_no = line_no + 1
            worksheet.write(
                f"A{line_no}",
                "Claim Amount",
                cell_format,
            )
            worksheet.write(
                f"B{line_no}",
                data["total_claims"],
                amount_format,
            )
            line_no = line_no + 1
            worksheet.write(
                f"A{line_no}",
                "Rebalancing Protected Advances",
                cell_format,
            )
            worksheet.write(
                f"B{line_no}",
                data["sent_to_trust"],
                amount_format,
            )
        elif data["from_trust"]:
            worksheet.write(
                f"A{line_no}",
                "Advances to be Release from Trust",
                bold_underline,
            )
            line_no = line_no + 1
            worksheet.write(
                f"A{line_no}",
                "Claim Amount",
                cell_format,
            )
            worksheet.write(
                f"B{line_no}",
                data["total_claims"],
                amount_format,
            )
            line_no = line_no + 1
            worksheet.write(
                f"A{line_no}",
                "Rebalancing Protected Advances",
                cell_format,
            )
            worksheet.write(
                f"B{line_no}",
                data["from_trust"],
                amount_format,
            )
        workbook.close()

    def movement_of_funds_excel_file(self, client_id, currency, from_date, to_date, user_id, file_name):
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        client_name = client_info.get("full_name", None) if client_info else None

        clients = get_db().user.find_one({"user_id": user_id}, projection={"clients": 1})["clients"]
        if client_id:
            clients = [ObjectId(client_id)]
        banking_match_condition = [
            {"client_id": {"$in": clients}, "status": {"$nin": ["Cancelled", "Cancelled by System"]}},
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$banking_files.file_date", -1]},
                        to_date if to_date else datetime.today().strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]
        claim_match_condition = [
            {"client_id": {"$in": clients}, "status": {"$nin": ["Cancelled", "Cancelled by System"]}},
            {
                "$expr": {
                    "$gte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        from_date if from_date else datetime.min.strftime("%Y-%m-%d"),
                    ]
                }
            },
            {
                "$expr": {
                    "$lte": [
                        {"$arrayElemAt": ["$claim_files.file_date", -1]},
                        to_date if to_date else datetime.today().strftime("%Y-%m-%d"),
                    ]
                }
            },
        ]
        total_payments = list(
            get_db().banking_metadata.aggregate(
                [
                    {"$match": {"$and": banking_match_condition}},
                    {
                        "$project": {
                            "amount": {"$last": f"$banking_files.deposit.{currency}"},
                            "_id": 0,
                        }
                    },
                    {"$group": {"_id": None, "totalAmount": {"$sum": "$amount"}}},
                ]
            )
        )
        total_payments = round(total_payments[0]["totalAmount"] if total_payments else 0, 2)
        total_claims = list(
            get_db().claims_metadata.aggregate(
                [
                    {"$match": {"$and": claim_match_condition}},
                    {
                        "$project": {
                            "amount": {"$last": f"$claim_files.claim_total.{currency}"},
                            "_id": 0,
                        }
                    },
                    {"$group": {"_id": None, "totalAmount": {"$sum": "$amount"}}},
                ]
            )
        )
        total_claims = round(total_claims[0]["totalAmount"] if total_claims else 0, 2)
        balance = round(total_payments - total_claims, 2)
        if isinstance(from_date, str):
            from_date = datetime.strptime(from_date, "%Y-%m-%d")
        if isinstance(to_date, str):
            to_date = datetime.strptime(to_date, "%Y-%m-%d")

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")

        worksheet = workbook.add_worksheet()
        bold_format = workbook.add_format({"bold": True})
        border_format = workbook.add_format({"border": 1})
        bold_border_format = workbook.add_format({"bold": True, "border": 1})

        worksheet.merge_range("A1:B1", "Movement Of Funds", bold_format)

        data = [
            ["Client Name", client_name],
            ["Currency code", currency],
            ["From Date", "To Date"],
            [from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d")],
            ["Total Payments", total_payments],
            ["Total Claims", total_claims],
            ["Balance", balance],
        ]

        row, col = 1, 0
        for item in data:
            worksheet.write(row, col, item[0], border_format)
            worksheet.write(row, col + 1, item[1], border_format)
            row += 1

        worksheet.set_column(0, 1, 25)

        workbook.close()

    def non_trust_bookings_excel_file(self, client_id, currency, from_date, to_date, file_name):
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        client_name = client_info.get("full_name", None) if client_info else None
        if from_date:
            from_date = datetime.fromisoformat(from_date)
        else:
            from_date = datetime.min
        if to_date:
            to_date = datetime.combine(datetime.fromisoformat(to_date), datetime.max.time())
        else:
            to_date = datetime.max

        pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "status": {"$nin": ["Cancelled", "Cancelled by System"]},
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                }
            },
            {"$unwind": "$banking_files"},
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "localField": "banking_files.file_id",
                    "foreignField": "file_id",
                    "as": "file_details",
                }
            },
            {"$unwind": "$file_details"},
            {"$match": {"file_details.non_trust_flag": True}},
            {
                "$group": {
                    "_id": "$banking_files.file_date",
                    "total_amount": {"$sum": "$file_details.bank_file_amount"},
                    "no_of_bookings": {"$sum": 1},
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "fileDate": "$_id",
                    "amount": {"$ifNull": ["$total_amount", 0]},
                    "noOfBookings": {"$ifNull": ["$no_of_bookings", 0]},
                }
            },
            {"$sort": {"fileDate": 1}},
        ]

        response = list(get_db().banking_metadata.aggregate(pipeline))
        if not response:

            total_amount = 0
            no_of_bookings = 0
        else:

            total_amount = response[0].get("amount", 0)
            no_of_bookings = response[0].get("noOfBookings", 0)

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")

        worksheet = workbook.add_worksheet()
        bold_format = workbook.add_format({"bold": True})
        border_format = workbook.add_format({"border": 1})

        worksheet.merge_range("A1:B1", "Non Trust Bookings", bold_format)

        data = [
            ["Client name", client_name],
            ["Currency code", currency],
            ["From Date", "To Date"],
            [from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d")],
            ["Total no of Bookings", no_of_bookings],
            ["Total Amount ", total_amount],
        ]

        row, col = 1, 0
        for item in data:
            worksheet.write(row, col, item[0], border_format)
            worksheet.write(row, col + 1, item[1], border_format)
            row += 1

        worksheet.set_column(0, 1, 25)

        workbook.close()

    def exposure_breakdown_excel_file(self, client_id, currency, from_date, to_date, file_name):
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        client_name = client_info.get("full_name", None) if client_info else None
        from_date = datetime.strptime(from_date, "%Y-%m-%d")
        to_date = datetime.strptime(to_date, "%Y-%m-%d")

        banking_metadata = list(
            get_db().banking_metadata.find(
                {
                    "client_id": ObjectId(client_id),
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                }
            )
        )
        total_non_bonded_amount = 0
        query = {"client_id": ObjectId(client_id), "booking_date": {"$gte": from_date, "$lte": to_date}}
        non_bond_info = get_db().banking_file_details.find(query)
        for document in non_bond_info:
            total_non_bonded_amount += document.get("non_bonded_amount", 0)

        sent_to_trust = 0
        file_ids = set()

        for metadata in banking_metadata:
            banking_files = metadata.get("banking_files", [])
            if not banking_files:
                continue
            # Sort files by file_date in descending order
            banking_files.sort(key=lambda x: x.get("file_date"), reverse=True)
            latest_file = banking_files[0]  # Take the latest file
            sent_to_trust += latest_file.get("deposit", {}).get(currency, 0)
            file_ids.add(latest_file.get("file_id"))

        # Step 2: Fetch banking_file_details and sum original amounts
        total_value = 0
        banking_file_details = list(
            get_db().banking_file_details.find(
                {"file_id": {"$in": list(file_ids)}, "deleted": False, "currency_code": currency}
            )
        )

        for file_detail in banking_file_details:
            total_value += file_detail.get("original_amount", 0)

        not_sent_to_trust = total_value - sent_to_trust

        bond_info = get_db().client_bond_info.find_one({"client_id": ObjectId(client_id)})
        bond_amount = bond_info.get("bond_amount", 0) if bond_info else 0

        sentToTrust = round(sent_to_trust, 2)
        totalValue = round(total_value, 2)
        notSentToTrust = round(not_sent_to_trust, 2)
        deductableAmount = round(bond_amount, 2)
        nonBondedAmount = total_non_bonded_amount

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")

        worksheet = workbook.add_worksheet()
        bold_format = workbook.add_format({"bold": True})
        border_format = workbook.add_format({"border": 1})
        bold_border_format = workbook.add_format({"bold": True, "border": 1})

        worksheet.merge_range("A1:B1", "Risk Exposure Breakdown", bold_format)

        data = [
            ["Client name", client_name],
            ["Currency code", currency],
            ["From Date", "To Date"],
            [from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d")],
            ["Total Value(100%)", total_value],
            ["Amount sent to Trust(40%)", sentToTrust],
            ["Amount not sent to Trust(60%)", notSentToTrust],
        ]

        row, col = 1, 0
        for item in data:
            worksheet.write(row, col, item[0], border_format)
            worksheet.write(row, col + 1, item[1], border_format)
            row += 1

        worksheet.set_column(0, 1, 25)

        workbook.close()

    def delayed_trust_funds_excel_file(self, currency, from_date, to_date, client_id, file_name):
        client_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        client_name = client_info.get("full_name", None) if client_info else None
        if from_date:
            from_date = datetime.strptime(from_date, "%Y-%m-%d")
        else:
            from_date = datetime.min

        if to_date:
            to_date = datetime.strptime(to_date, "%Y-%m-%d")
        else:
            to_date = datetime.today()

        aggregation_pipeline = [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "banking_files.file_date": {
                        "$gte": from_date.strftime("%Y-%m-%d"),
                        "$lte": to_date.strftime("%Y-%m-%d"),
                    },
                    "banking_files.status": {
                        "$nin": ["authorized", "Cancelled by System", "Cancelled", "Authorised", "authorised"]
                    },
                }
            },
            {"$unwind": "$banking_files"},
            {
                "$project": {
                    "file_id": "$banking_files.file_id",
                    "file_date": "$banking_files.file_date",
                    "deposit": "$banking_files.deposit",
                }
            },
            {
                "$group": {
                    "_id": None,
                    "file_info": {"$push": {"file_id": "$file_id", "file_date": "$file_date", "deposit": "$deposit"}},
                }
            },
        ]

        file_ids_result = list(get_db().banking_metadata.aggregate(aggregation_pipeline))

        file_info = file_ids_result[0].get("file_info", []) if file_ids_result else []

        response = []
        if file_info:
            for info in file_info:
                file_date = datetime.strptime(info["file_date"], "%Y-%m-%d")
                deposit = info.get("deposit", {})
                escrow_amount = deposit.get(currency, 0)

                days_diff = (datetime.today() - file_date).days

                if days_diff > 4:
                    risk_level = "High"
                elif days_diff > 3:
                    risk_level = "Medium"
                elif days_diff > 2:
                    risk_level = "Low"
                else:
                    risk_level = "None"

                if escrow_amount != 0:

                    response.append(
                        {"amount": escrow_amount, "fileDate": file_date.strftime("%Y-%m-%d"), "riskLevel": risk_level}
                    )

        response = sorted(response, key=lambda x: x["fileDate"])
        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")

        worksheet = workbook.add_worksheet()
        bold_format = workbook.add_format({"bold": True})
        border_format = workbook.add_format({"border": 1})
        bold_border_format = workbook.add_format({"bold": True, "border": 1})

        worksheet.merge_range("A1:B1", "Delayed Trust Funds", bold_format)

        data = [
            ["Client name", client_name],
            ["Currency code", currency],
            ["From Date", "To Date"],
            [from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d")],
        ]

        row, col = 1, 0
        for item in data:
            worksheet.write(row, col, item[0], border_format)
            worksheet.write(row, col + 1, item[1], border_format)
            row += 1

        row += 1

        worksheet.write(row, 0, "Amount", border_format)
        worksheet.write(row, 1, "File Date", border_format)
        worksheet.write(row, 2, "Risk", border_format)
        row += 1

        for record in response:
            worksheet.write(row, 0, record["amount"], border_format)

            # If fileDate is already a string, no need to call strftime
            file_date = record["fileDate"]
            if isinstance(file_date, str):
                worksheet.write(row, 1, file_date, border_format)
            else:
                worksheet.write(row, 1, file_date.strftime("%Y-%m-%d"), border_format)
            worksheet.write(row, 2, record["riskLevel"], border_format)
            row += 1

        worksheet.set_column(0, 1, 25)

        workbook.close()

    def export_weekly_barclays_report(self, open_banking_data, name, client_name):
        # basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client_id)})
        today = time.strftime("%d/%m/%Y")

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{name}")
        worksheet = workbook.add_worksheet()
        worksheet.set_column("A:A", 60)
        worksheet.set_column("B:B", 20)

        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        merge_left_1 = workbook.add_format({"size": 11, "align": "left"})
        merge_center = workbook.add_format({"bold": 1, "size": 11, "align": "center", "valign": "vcenter"})
        amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right", "valign": "vcenter"})
        amount_format_red = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "color": "red"}
        )
        cell_format = workbook.add_format()
        cell_format.set_text_wrap()
        worksheet.write("A1", f"{client_name}", merge_left)
        worksheet.write("A2", "Statement of Trust Account", merge_left)
        worksheet.write("A3", f"For the Compliance Reporting Day of {today}", merge_left)
        worksheet.write("B3", "Trust Account", merge_left)

        if open_banking_data:
            worksheet.write("B6", open_banking_data["funds_in"], amount_format)
            worksheet.write(
                "A6",
                "Total amount that has been paid into the Trustee Account since\n the Prior Compliance Reporting Day",
                cell_format,
            )
            worksheet.write("B8", open_banking_data["funds_out"], amount_format_red)
            worksheet.write(
                "A8",
                "Total amount that has been paid out by the Trustee in response to\n Payment Requests since the Prior Compliance Reporting Day",
                cell_format,
            )

        worksheet.write("A11", f"{client_name} certify this statement as being true and accurate", merge_left_1)
        worksheet.write("A13", "Signed", merge_left_1)
        worksheet.write("A17", "Position", merge_left_1)
        worksheet.write("A21", "Date", merge_left_1)
        workbook.close()

    def export_barclays_statement_report(
        self, open_banking_data, client_name, from_date, to_date, account_no, file_name, currency, iban, sort_code
    ):
        transactions = open_banking_data["data"]["attributes"]["transactionHistoryDetails"][0]["transactions"]

        account_transactions = []
        for txn in transactions:
            txn_date = txn["valueDateTime"]

            if from_date <= txn_date <= to_date:
                account_transactions.append(
                    {
                        "transactionId": txn["transactionId"],
                        "amount": txn["amount"]["amount"],
                        "currency": txn["amount"]["currency"],
                        "bookingDateTime": txn["bookingDateTime"],
                        "valueDateTime": txn["valueDateTime"],
                        "balance": txn["balance"]["amount"]["amount"],
                        "transactionInformation": txn["transactionInformation"],
                        "transaction_type": txn["creditDebitIndicator"],
                    }
                )

        new_list = sorted(account_transactions, key=lambda x: x["valueDateTime"])

        from_date = change_month_format(from_date)
        to_date = change_month_format(to_date)

        workbook = xlsxwriter.Workbook(f"{current_app.config['TEMP_DIR']}/{file_name}")
        worksheet = workbook.add_worksheet()

        worksheet.set_column("A:A", 15)
        worksheet.set_column("B:B", 15)
        worksheet.set_column("C:C", 20)
        worksheet.set_column("D:D", 50)
        worksheet.set_column("E:E", 15)
        worksheet.set_column("F:F", 15)
        worksheet.set_column("G:G", 20)
        worksheet.set_column("H:H", 15)

        amount_format = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "border": 1}
        )
        merge_left = workbook.add_format({"bold": 1, "size": 11, "align": "left"})
        amount_format_red = workbook.add_format(
            {"num_format": "#,##0.00", "align": "right", "valign": "vcenter", "color": "red", "border": 1}
        )
        border_format = workbook.add_format({"border": 1})
        bold_format = workbook.add_format({"bold": True, "border": 1})

        worksheet.write("A1", "Barclays", merge_left)
        worksheet.write("A2", f"{client_name} - Transaction Report", merge_left)
        worksheet.write("A3", "Account Statement", merge_left)

        worksheet.write("A4", f"Account No: {account_no}", merge_left)
        worksheet.write("A5", f"IBAN - {iban}", merge_left)
        worksheet.write("A6", f"Bank Identifier - {sort_code}", merge_left)

        worksheet.write("A7", f"Currency: {currency}", merge_left)
        worksheet.write("A8", f"From Date: {from_date}", merge_left)
        worksheet.write("A9", f"To Date: {to_date}", merge_left)

        worksheet.write("A11", "Value Date", bold_format)
        worksheet.write("B11", "Booking Date", bold_format)
        worksheet.write("C11", "Transaction ID", bold_format)
        worksheet.write("D11", "Transaction Info", bold_format)
        worksheet.write("E11", "Transaction Type", bold_format)
        worksheet.write("F11", "Currency", bold_format)
        worksheet.write("G11", "Amount", bold_format)
        worksheet.write("H11", "Balance", bold_format)
        row = 11

        for txn in new_list:
            worksheet.write(
                row,
                0,
                datetime.strptime(txn["valueDateTime"], "%Y-%m-%dT%H:%M:%S%z").strftime("%d-%m-%Y"),
                border_format,
            )
            worksheet.write(
                row,
                1,
                datetime.strptime(txn["bookingDateTime"], "%Y-%m-%dT%H:%M:%S%z").strftime("%d-%m-%Y"),
                border_format,
            )
            worksheet.write(row, 2, txn["transactionId"], border_format)
            worksheet.write(row, 3, txn["transactionInformation"], border_format)
            if txn["transaction_type"] == "Credit":
                worksheet.write(row, 4, "Credit", border_format)
            else:
                worksheet.write(row, 4, "Debit", border_format)
            worksheet.write(row, 5, txn["currency"], border_format)
            if txn["transaction_type"] == "Credit":
                worksheet.write(row, 6, txn["amount"], amount_format)
            else:
                worksheet.write(row, 6, txn["amount"], amount_format_red)

            worksheet.write(row, 7, txn["balance"], amount_format)
            row += 1

        workbook.close()
        # sftp_bucket = current_app.config["SFTP_BUCKET"]
        sftp_bucket = current_app.config["PTT_BUCKET"]
        s3_key = f'{"banking_summary_report/"}{file_name}'
        upload_file(bucket=sftp_bucket, key=s3_key, file_path=f"{current_app.config['TEMP_DIR']}/{file_name}")


reporting_service = ReportingService()
