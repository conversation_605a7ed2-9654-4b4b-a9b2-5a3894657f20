from flask import current_app
from flaskr.models import get_db
from datetime import datetime, timedelta
from bson import ObjectId
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Callable

logger = logging.getLogger(__name__)


class PowerBICalculationEngine:
    """
    Flexible calculation engine for PowerBI report-specific computed fields
    """
    
    def __init__(self):
        self.db = get_db()
        self.calculation_functions = self._register_calculation_functions()
    
    def _register_calculation_functions(self) -> Dict[str, Callable]:
        """
        Register all available calculation functions
        
        Returns:
            dict: Dictionary of function name -> function
        """
        return {
            # Basic arithmetic
            'sum': self._sum,
            'average': self._average,
            'count': self._count,
            'min': self._min,
            'max': self._max,
            
            # Financial calculations
            'balance': self._calculate_balance,
            'percentage': self._calculate_percentage,
            'ratio': self._calculate_ratio,
            'variance': self._calculate_variance,
            'growth_rate': self._calculate_growth_rate,
            
            # Date calculations
            'days_between': self._days_between,
            'months_between': self._months_between,
            'age_in_days': self._age_in_days,
            'quarter': self._get_quarter,
            'month_name': self._get_month_name,
            
            # Aggregations
            'group_sum': self._group_sum,
            'group_average': self._group_average,
            'group_count': self._group_count,
            'running_total': self._running_total,
            
            # Conditional calculations
            'if_then_else': self._if_then_else,
            'case_when': self._case_when,
            'lookup': self._lookup_value,
            
            # Currency conversions
            'convert_currency': self._convert_currency,
            'multi_currency_sum': self._multi_currency_sum,
            
            # Custom business logic
            'trust_balance': self._calculate_trust_balance,
            'exposure_ratio': self._calculate_exposure_ratio,
            'claim_frequency': self._calculate_claim_frequency,
            'booking_status': self._determine_booking_status
        }
    
    def calculate_report_data(self, report_config: Dict, banking_data: List[Dict], claims_data: List[Dict]) -> List[Dict]:
        """
        Calculate report-specific data based on configuration
        
        Args:
            report_config (dict): Report configuration with calculation rules
            banking_data (list): Raw banking data
            claims_data (list): Raw claims data
            
        Returns:
            list: Calculated data ready for PowerBI
        """
        current_app.logger.info(f"Calculating data for report: {report_config.get('name', 'Unknown')}")
        
        # Convert to DataFrames for easier manipulation
        banking_df = pd.DataFrame(banking_data) if banking_data else pd.DataFrame()
        claims_df = pd.DataFrame(claims_data) if claims_data else pd.DataFrame()
        
        # Get calculation rules from config
        calculation_rules = report_config.get('calculation_rules', [])
        grouping_config = report_config.get('grouping', {})
        filters = report_config.get('filters', [])
        
        # Apply filters first
        if filters:
            banking_df = self._apply_filters(banking_df, filters)
            claims_df = self._apply_filters(claims_df, filters)
        
        # Merge banking and claims data if needed
        merged_data = self._merge_data(banking_df, claims_df, grouping_config)
        
        # Apply calculations
        calculated_data = self._apply_calculations(merged_data, calculation_rules)
        
        # Convert back to list of dictionaries
        result = calculated_data.to_dict('records') if not calculated_data.empty else []
        
        current_app.logger.info(f"Generated {len(result)} calculated records for report")
        return result
    
    def _merge_data(self, banking_df: pd.DataFrame, claims_df: pd.DataFrame, grouping_config: Dict) -> pd.DataFrame:
        """
        Merge banking and claims data based on grouping configuration
        """
        if banking_df.empty and claims_df.empty:
            return pd.DataFrame()
        
        merge_key = grouping_config.get('merge_key', 'ClientID')
        merge_type = grouping_config.get('merge_type', 'outer')
        
        if banking_df.empty:
            return claims_df
        if claims_df.empty:
            return banking_df
        
        # Merge on the specified key
        merged = pd.merge(banking_df, claims_df, on=merge_key, how=merge_type, suffixes=('_banking', '_claims'))
        
        return merged
    
    def _apply_filters(self, df: pd.DataFrame, filters: List[Dict]) -> pd.DataFrame:
        """
        Apply filters to the dataframe
        """
        if df.empty:
            return df
        
        for filter_rule in filters:
            column = filter_rule.get('column')
            operator = filter_rule.get('operator')
            value = filter_rule.get('value')
            
            if column not in df.columns:
                continue
            
            if operator == 'equals':
                df = df[df[column] == value]
            elif operator == 'not_equals':
                df = df[df[column] != value]
            elif operator == 'greater_than':
                df = df[df[column] > value]
            elif operator == 'less_than':
                df = df[df[column] < value]
            elif operator == 'contains':
                df = df[df[column].str.contains(str(value), na=False)]
            elif operator == 'in':
                df = df[df[column].isin(value)]
        
        return df
    
    def _apply_calculations(self, df: pd.DataFrame, calculation_rules: List[Dict]) -> pd.DataFrame:
        """
        Apply calculation rules to create computed columns
        """
        if df.empty:
            return df
        
        for rule in calculation_rules:
            column_name = rule.get('output_column')
            function_name = rule.get('function')
            parameters = rule.get('parameters', {})
            
            if function_name in self.calculation_functions:
                try:
                    df[column_name] = self.calculation_functions[function_name](df, parameters)
                except Exception as e:
                    current_app.logger.error(f"Error calculating {column_name}: {str(e)}")
                    df[column_name] = None
        
        return df
    
    # Basic calculation functions
    def _sum(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        columns = params.get('columns', [])
        return df[columns].sum(axis=1)
    
    def _average(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        columns = params.get('columns', [])
        return df[columns].mean(axis=1)
    
    def _count(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        column = params.get('column')
        return df[column].notna().astype(int) if column else len(df)
    
    def _min(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        columns = params.get('columns', [])
        return df[columns].min(axis=1)
    
    def _max(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        columns = params.get('columns', [])
        return df[columns].max(axis=1)
    
    # Financial calculations
    def _calculate_balance(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate balance: deposits - refunds - claims"""
        deposits_col = params.get('deposits_column', 'TotalDeposits')
        refunds_col = params.get('refunds_column', 'TotalRefunds')
        claims_col = params.get('claims_column', 'TotalClaims')
        
        deposits = df.get(deposits_col, 0)
        refunds = df.get(refunds_col, 0)
        claims = df.get(claims_col, 0)
        
        return deposits - refunds - claims
    
    def _calculate_percentage(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate percentage: (numerator / denominator) * 100"""
        numerator_col = params.get('numerator')
        denominator_col = params.get('denominator')
        
        return (df[numerator_col] / df[denominator_col] * 100).fillna(0)
    
    def _calculate_ratio(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate ratio between two columns"""
        numerator_col = params.get('numerator')
        denominator_col = params.get('denominator')
        
        return (df[numerator_col] / df[denominator_col]).fillna(0)
    
    def _calculate_variance(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate variance between actual and expected"""
        actual_col = params.get('actual')
        expected_col = params.get('expected')
        
        return df[actual_col] - df[expected_col]
    
    def _calculate_growth_rate(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate growth rate: ((current - previous) / previous) * 100"""
        current_col = params.get('current')
        previous_col = params.get('previous')
        
        return ((df[current_col] - df[previous_col]) / df[previous_col] * 100).fillna(0)
    
    # Date calculations
    def _days_between(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate days between two dates"""
        start_col = params.get('start_date')
        end_col = params.get('end_date')
        
        start_dates = pd.to_datetime(df[start_col])
        end_dates = pd.to_datetime(df[end_col])
        
        return (end_dates - start_dates).dt.days
    
    def _months_between(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate months between two dates"""
        start_col = params.get('start_date')
        end_col = params.get('end_date')
        
        start_dates = pd.to_datetime(df[start_col])
        end_dates = pd.to_datetime(df[end_col])
        
        return ((end_dates.dt.year - start_dates.dt.year) * 12 + 
                (end_dates.dt.month - start_dates.dt.month))
    
    def _age_in_days(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate age in days from a date to today"""
        date_col = params.get('date_column')
        reference_date = params.get('reference_date', datetime.utcnow())
        
        dates = pd.to_datetime(df[date_col])
        return (reference_date - dates).dt.days
    
    def _get_quarter(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Get quarter from date"""
        date_col = params.get('date_column')
        return pd.to_datetime(df[date_col]).dt.quarter
    
    def _get_month_name(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Get month name from date"""
        date_col = params.get('date_column')
        return pd.to_datetime(df[date_col]).dt.month_name()

    # Aggregation functions
    def _group_sum(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Group by column and sum values"""
        group_col = params.get('group_by')
        sum_col = params.get('sum_column')

        grouped = df.groupby(group_col)[sum_col].sum()
        return df[group_col].map(grouped)

    def _group_average(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Group by column and calculate average"""
        group_col = params.get('group_by')
        avg_col = params.get('average_column')

        grouped = df.groupby(group_col)[avg_col].mean()
        return df[group_col].map(grouped)

    def _group_count(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Group by column and count records"""
        group_col = params.get('group_by')

        grouped = df.groupby(group_col).size()
        return df[group_col].map(grouped)

    def _running_total(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate running total"""
        column = params.get('column')
        sort_by = params.get('sort_by', None)

        if sort_by:
            df_sorted = df.sort_values(sort_by)
            return df_sorted[column].cumsum()
        else:
            return df[column].cumsum()

    # Conditional functions
    def _if_then_else(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """If-then-else logic"""
        condition_col = params.get('condition_column')
        condition_op = params.get('condition_operator', 'equals')
        condition_val = params.get('condition_value')
        true_value = params.get('true_value')
        false_value = params.get('false_value')

        if condition_op == 'equals':
            condition = df[condition_col] == condition_val
        elif condition_op == 'greater_than':
            condition = df[condition_col] > condition_val
        elif condition_op == 'less_than':
            condition = df[condition_col] < condition_val
        elif condition_op == 'not_null':
            condition = df[condition_col].notna()
        else:
            condition = df[condition_col] == condition_val

        return np.where(condition, true_value, false_value)

    def _case_when(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Case when logic with multiple conditions"""
        cases = params.get('cases', [])
        default_value = params.get('default', None)

        result = pd.Series([default_value] * len(df), index=df.index)

        for case in cases:
            condition_col = case.get('condition_column')
            condition_op = case.get('condition_operator', 'equals')
            condition_val = case.get('condition_value')
            return_value = case.get('return_value')

            if condition_op == 'equals':
                mask = df[condition_col] == condition_val
            elif condition_op == 'greater_than':
                mask = df[condition_col] > condition_val
            elif condition_op == 'less_than':
                mask = df[condition_col] < condition_val
            elif condition_op == 'contains':
                mask = df[condition_col].str.contains(str(condition_val), na=False)
            else:
                mask = df[condition_col] == condition_val

            result.loc[mask] = return_value

        return result

    def _lookup_value(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Lookup values from another dataset"""
        lookup_table = params.get('lookup_table', {})
        lookup_column = params.get('lookup_column')
        default_value = params.get('default_value', None)

        return df[lookup_column].map(lookup_table).fillna(default_value)

    # Currency functions
    def _convert_currency(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Convert currency using exchange rates"""
        amount_col = params.get('amount_column')
        from_currency_col = params.get('from_currency_column')
        to_currency = params.get('to_currency', 'GBP')
        exchange_rates = params.get('exchange_rates', {})

        result = df[amount_col].copy()

        for from_currency, rate in exchange_rates.items():
            mask = df[from_currency_col] == from_currency
            result.loc[mask] = result.loc[mask] * rate

        return result

    def _multi_currency_sum(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Sum amounts across multiple currency columns"""
        currency_columns = params.get('currency_columns', {})  # {'GBP': 'col1', 'EUR': 'col2'}
        base_currency = params.get('base_currency', 'GBP')
        exchange_rates = params.get('exchange_rates', {'GBP': 1.0, 'EUR': 0.85, 'USD': 0.75})

        total = pd.Series([0.0] * len(df), index=df.index)

        for currency, column in currency_columns.items():
            if column in df.columns:
                rate = exchange_rates.get(currency, 1.0)
                total += df[column].fillna(0) * rate

        return total

    # Business-specific calculations
    def _calculate_trust_balance(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate trust account balance with business rules"""
        deposits_cols = params.get('deposits_columns', ['DepositGBP', 'DepositEUR', 'DepositUSD'])
        refunds_cols = params.get('refunds_columns', ['RefundGBP', 'RefundEUR', 'RefundUSD'])
        claims_cols = params.get('claims_columns', ['ClaimTotalGBP', 'ClaimTotalEUR', 'ClaimTotalUSD'])
        exchange_rates = params.get('exchange_rates', {'GBP': 1.0, 'EUR': 0.85, 'USD': 0.75})

        # Convert all to base currency and sum
        total_deposits = self._multi_currency_sum(df, {
            'currency_columns': {curr: col for curr, col in zip(['GBP', 'EUR', 'USD'], deposits_cols)},
            'exchange_rates': exchange_rates
        })

        total_refunds = self._multi_currency_sum(df, {
            'currency_columns': {curr: col for curr, col in zip(['GBP', 'EUR', 'USD'], refunds_cols)},
            'exchange_rates': exchange_rates
        })

        total_claims = self._multi_currency_sum(df, {
            'currency_columns': {curr: col for curr, col in zip(['GBP', 'EUR', 'USD'], claims_cols)},
            'exchange_rates': exchange_rates
        })

        return total_deposits - total_refunds - total_claims

    def _calculate_exposure_ratio(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate exposure ratio for risk assessment"""
        claims_col = params.get('claims_column', 'TotalClaims')
        deposits_col = params.get('deposits_column', 'TotalDeposits')
        max_ratio = params.get('max_ratio', 1.0)

        ratio = (df[claims_col] / df[deposits_col]).fillna(0)
        return np.minimum(ratio, max_ratio)

    def _calculate_claim_frequency(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Calculate claim frequency per client"""
        client_col = params.get('client_column', 'ClientID')
        date_col = params.get('date_column', 'FileDate')
        period_days = params.get('period_days', 365)

        # Group by client and count claims in the period
        df['date_parsed'] = pd.to_datetime(df[date_col])
        cutoff_date = datetime.utcnow() - timedelta(days=period_days)

        recent_claims = df[df['date_parsed'] >= cutoff_date]
        frequency = recent_claims.groupby(client_col).size()

        return df[client_col].map(frequency).fillna(0)

    def _determine_booking_status(self, df: pd.DataFrame, params: Dict) -> pd.Series:
        """Determine booking status based on business rules"""
        balance_col = params.get('balance_column', 'Balance')
        days_to_departure_col = params.get('days_to_departure_column', 'DaysToDeparture')

        conditions = [
            (df[balance_col] <= 0) & (df[days_to_departure_col] > 30),
            (df[balance_col] <= 0) & (df[days_to_departure_col] <= 30),
            (df[balance_col] > 0) & (df[days_to_departure_col] <= 7),
            df[balance_col] > 0
        ]

        choices = ['Fully Paid - Early', 'Fully Paid - Near Departure', 'Outstanding - Urgent', 'Outstanding']

        return pd.Series(np.select(conditions, choices, default='Unknown'), index=df.index)


powerbi_calculation_engine = PowerBICalculationEngine()
