from flask import current_app
from flaskr.models import get_db
from datetime import datetime
import logging
import json

logger = logging.getLogger(__name__)


class PowerBIReportConfigService:
    """
    Service for managing PowerBI report-specific calculation configurations
    """
    
    def __init__(self):
        self.db = get_db()
    
    def get_report_config(self, report_id: str) -> dict:
        """
        Get calculation configuration for a specific report
        
        Args:
            report_id (str): PowerBI report ID
            
        Returns:
            dict: Report configuration with calculation rules
        """
        config = self.db.powerbi_report_configs.find_one({"report_id": report_id})
        
        if not config:
            # Return default configuration based on report type
            return self._get_default_config(report_id)
        
        current_app.logger.info(f"Retrieved configuration for report: {report_id}")
        return config
    
    def save_report_config(self, report_id: str, config: dict) -> bool:
        """
        Save calculation configuration for a report
        
        Args:
            report_id (str): PowerBI report ID
            config (dict): Report configuration
            
        Returns:
            bool: True if successful
        """
        config["report_id"] = report_id
        config["updated_at"] = datetime.utcnow()
        
        if "created_at" not in config:
            config["created_at"] = datetime.utcnow()
        
        self.db.powerbi_report_configs.update_one(
            {"report_id": report_id},
            {"$set": config},
            upsert=True
        )
        
        current_app.logger.info(f"Saved configuration for report: {report_id}")
        return True
    
    def _get_default_config(self, report_id: str) -> dict:
        """
        Get default configuration based on report ID
        """
        # Check if this is the main dashboard or ERV dashboard
        main_report_id = current_app.config.get("POWERBI_REPORT_ID")
        erv_report_id = current_app.config.get("POWERBI_ERV_REPORT_ID")
        
        if report_id == main_report_id:
            return self._get_main_dashboard_config()
        elif report_id == erv_report_id:
            return self._get_erv_dashboard_config()
        else:
            return self._get_generic_config()
    
    def _get_main_dashboard_config(self) -> dict:
        """
        Default configuration for main dashboard
        """
        return {
            "name": "Main Dashboard",
            "description": "Primary banking and claims dashboard",
            "grouping": {
                "merge_key": "ClientID",
                "merge_type": "outer"
            },
            "filters": [
                {
                    "column": "Status",
                    "operator": "not_equals",
                    "value": "Cancelled"
                }
            ],
            "calculation_rules": [
                {
                    "output_column": "TotalDepositsGBP",
                    "function": "multi_currency_sum",
                    "parameters": {
                        "currency_columns": {
                            "GBP": "DepositGBP",
                            "EUR": "DepositEUR", 
                            "USD": "DepositUSD"
                        },
                        "base_currency": "GBP",
                        "exchange_rates": {"GBP": 1.0, "EUR": 0.85, "USD": 0.75}
                    }
                },
                {
                    "output_column": "TotalRefundsGBP",
                    "function": "multi_currency_sum",
                    "parameters": {
                        "currency_columns": {
                            "GBP": "RefundGBP",
                            "EUR": "RefundEUR",
                            "USD": "RefundUSD"
                        },
                        "base_currency": "GBP",
                        "exchange_rates": {"GBP": 1.0, "EUR": 0.85, "USD": 0.75}
                    }
                },
                {
                    "output_column": "TotalClaimsGBP",
                    "function": "multi_currency_sum",
                    "parameters": {
                        "currency_columns": {
                            "GBP": "ClaimTotalGBP",
                            "EUR": "ClaimTotalEUR",
                            "USD": "ClaimTotalUSD"
                        },
                        "base_currency": "GBP",
                        "exchange_rates": {"GBP": 1.0, "EUR": 0.85, "USD": 0.75}
                    }
                },
                {
                    "output_column": "TrustBalance",
                    "function": "calculate_balance",
                    "parameters": {
                        "deposits_column": "TotalDepositsGBP",
                        "refunds_column": "TotalRefundsGBP",
                        "claims_column": "TotalClaimsGBP"
                    }
                },
                {
                    "output_column": "ExposureRatio",
                    "function": "calculate_ratio",
                    "parameters": {
                        "numerator": "TotalClaimsGBP",
                        "denominator": "TotalDepositsGBP"
                    }
                },
                {
                    "output_column": "DaysToUpdate",
                    "function": "age_in_days",
                    "parameters": {
                        "date_column": "UpdatedAt"
                    }
                },
                {
                    "output_column": "RiskCategory",
                    "function": "case_when",
                    "parameters": {
                        "cases": [
                            {
                                "condition_column": "ExposureRatio",
                                "condition_operator": "greater_than",
                                "condition_value": 0.8,
                                "return_value": "High Risk"
                            },
                            {
                                "condition_column": "ExposureRatio",
                                "condition_operator": "greater_than",
                                "condition_value": 0.5,
                                "return_value": "Medium Risk"
                            }
                        ],
                        "default": "Low Risk"
                    }
                },
                {
                    "output_column": "Quarter",
                    "function": "get_quarter",
                    "parameters": {
                        "date_column": "FileDate"
                    }
                },
                {
                    "output_column": "MonthName",
                    "function": "get_month_name",
                    "parameters": {
                        "date_column": "FileDate"
                    }
                }
            ],
            "output_columns": [
                "ClientID", "ClientName", "FriendlyName",
                "TotalDepositsGBP", "TotalRefundsGBP", "TotalClaimsGBP",
                "TrustBalance", "ExposureRatio", "RiskCategory",
                "DaysToUpdate", "Quarter", "MonthName",
                "FileDate", "UpdatedAt"
            ]
        }
    
    def _get_erv_dashboard_config(self) -> dict:
        """
        Default configuration for ERV dashboard
        """
        return {
            "name": "ERV Dashboard",
            "description": "ERV-specific reporting dashboard",
            "grouping": {
                "merge_key": "ClientID",
                "merge_type": "outer"
            },
            "filters": [
                {
                    "column": "ClientName",
                    "operator": "contains",
                    "value": "ERV"
                }
            ],
            "calculation_rules": [
                {
                    "output_column": "NetPosition",
                    "function": "calculate_trust_balance",
                    "parameters": {
                        "deposits_columns": ["DepositGBP", "DepositEUR", "DepositUSD"],
                        "refunds_columns": ["RefundGBP", "RefundEUR", "RefundUSD"],
                        "claims_columns": ["ClaimTotalGBP", "ClaimTotalEUR", "ClaimTotalUSD"],
                        "exchange_rates": {"GBP": 1.0, "EUR": 0.85, "USD": 0.75}
                    }
                },
                {
                    "output_column": "ClaimFrequency",
                    "function": "calculate_claim_frequency",
                    "parameters": {
                        "client_column": "ClientID",
                        "date_column": "FileDate",
                        "period_days": 365
                    }
                },
                {
                    "output_column": "BookingStatus",
                    "function": "determine_booking_status",
                    "parameters": {
                        "balance_column": "NetPosition",
                        "days_to_departure_column": "DaysToDeparture"
                    }
                },
                {
                    "output_column": "VarianceFromExpected",
                    "function": "calculate_variance",
                    "parameters": {
                        "actual": "TotalClaimsGBP",
                        "expected": "ExpectedClaims"
                    }
                },
                {
                    "output_column": "GrowthRate",
                    "function": "calculate_growth_rate",
                    "parameters": {
                        "current": "TotalDepositsGBP",
                        "previous": "PreviousDeposits"
                    }
                }
            ],
            "output_columns": [
                "ClientID", "ClientName", "FriendlyName",
                "NetPosition", "ClaimFrequency", "BookingStatus",
                "VarianceFromExpected", "GrowthRate",
                "FileDate", "UpdatedAt"
            ]
        }
    
    def _get_generic_config(self) -> dict:
        """
        Generic configuration for unknown reports
        """
        return {
            "name": "Generic Report",
            "description": "Basic banking and claims data",
            "grouping": {
                "merge_key": "ClientID",
                "merge_type": "outer"
            },
            "filters": [],
            "calculation_rules": [
                {
                    "output_column": "TotalBalance",
                    "function": "calculate_balance",
                    "parameters": {
                        "deposits_column": "DepositGBP",
                        "refunds_column": "RefundGBP",
                        "claims_column": "ClaimTotalGBP"
                    }
                }
            ],
            "output_columns": [
                "ClientID", "ClientName", "TotalBalance", "FileDate", "UpdatedAt"
            ]
        }
    
    def get_all_report_configs(self) -> list:
        """
        Get all report configurations
        
        Returns:
            list: List of all report configurations
        """
        configs = list(self.db.powerbi_report_configs.find())
        
        # Convert ObjectId to string for JSON serialization
        for config in configs:
            if "_id" in config:
                config["_id"] = str(config["_id"])
            if "created_at" in config:
                config["created_at"] = config["created_at"].isoformat()
            if "updated_at" in config:
                config["updated_at"] = config["updated_at"].isoformat()
        
        return configs
    
    def delete_report_config(self, report_id: str) -> bool:
        """
        Delete a report configuration
        
        Args:
            report_id (str): PowerBI report ID
            
        Returns:
            bool: True if deleted
        """
        result = self.db.powerbi_report_configs.delete_one({"report_id": report_id})
        
        if result.deleted_count > 0:
            current_app.logger.info(f"Deleted configuration for report: {report_id}")
            return True
        
        return False
    
    def validate_config(self, config: dict) -> tuple:
        """
        Validate a report configuration
        
        Args:
            config (dict): Configuration to validate
            
        Returns:
            tuple: (is_valid, error_messages)
        """
        errors = []
        
        # Check required fields
        required_fields = ["name", "calculation_rules", "output_columns"]
        for field in required_fields:
            if field not in config:
                errors.append(f"Missing required field: {field}")
        
        # Validate calculation rules
        if "calculation_rules" in config:
            for i, rule in enumerate(config["calculation_rules"]):
                if "output_column" not in rule:
                    errors.append(f"Rule {i}: Missing output_column")
                if "function" not in rule:
                    errors.append(f"Rule {i}: Missing function")
        
        return len(errors) == 0, errors


powerbi_report_config_service = PowerBIReportConfigService()
