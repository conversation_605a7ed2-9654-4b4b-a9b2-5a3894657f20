import re
from datetime import datetime, timezone
import calendar
from calendar import isleap


def change_date_format(dt):
    return re.sub(r"(\d{4})-(\d{1,2})-(\d{1,2})", "\\3/\\2/\\1", dt) if dt else None


def find_day(date):
    day = datetime.fromisoformat(date).weekday()
    return calendar.day_name[day]


def find_time(date):
    time = datetime.fromisoformat(date)
    time = time.strftime("%H:%M")
    return time


def change_month_format(date):
    date = str(date)[:10]
    month = datetime.strptime(date, "%Y-%m-%d")
    return month.strftime("%d %B %Y")


def monthly_dates(year):
    year = int(year)
    months_choices = []
    months = (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)
    for i in months:
        month = datetime(year, i, 1).strftime("%b")
        num = datetime(year, i, 1).strftime("%m")

        startDate = f"01-{num}-{year}"
        if month in ["Jan", "Mar", "May", "Jul", "Aug", "Oct", "Dec"]:
            endDate = f"31-{num}-{year}"
        elif month in ["Apr", "Jun", "Sep", "Nov"]:
            endDate = f"30-{num}-{year}"
        else:
            isLeap = isleap(year)
            if isLeap:
                endDate = f"29-{num}-{year}"
            else:
                endDate = f"28-{num}-{year}"
        months_choices.append((startDate, endDate))
    return months_choices


def format_ordinal_date(input_date):

    # Extract day, month, and year
    day_number = input_date.day
    month_name = input_date.strftime("%B")
    year = input_date.year

    # Determine the ordinal suffix for the day
    if 10 <= day_number % 100 <= 20:
        suffix = "th"
    else:
        suffix = {1: "st", 2: "nd", 3: "rd"}.get(day_number % 10, "th")

    formatted_date = f"{day_number}{suffix} {month_name} {year}"
    return formatted_date


def date_format(value):
    for date in (
        "%Y-%m-%d",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y/%m/%d",
        "%d-%m-%Y",
        "%d-%m-%Y %H:%M:%S",
        "%d-%m-%Y %H:%M:%S.%f",
        "%d/%m/%Y",
    ):
        try:
            return datetime.strptime(value, date).strftime("%Y-%m-%d")
        except ValueError:
            pass
    raise ValueError()


def format_date_with_day_ordinal(input_date):
    # Parse the input date string
    if isinstance(input_date, datetime):
        date_obj = input_date
    else:
        # Parse the input date string
        date_obj = datetime.strptime(input_date, "%d/%m/%Y")

    # Get the day without leading zero and add the appropriate suffix
    day = date_obj.strftime("%d").lstrip("0")
    if day.endswith("1") and day != "11":
        day += "st"
    elif day.endswith("2") and day != "12":
        day += "nd"
    elif day.endswith("3") and day != "13":
        day += "rd"
    else:
        day += "th"

    # Get the month name
    month = date_obj.strftime("%B")

    # Get the year
    year = date_obj.strftime("%Y")

    # Format the date string
    formatted_date = f"{day} {month} {year}"
    return formatted_date


def format_date_for_email_template(date):
    """Converts a date from the format YYYY-MM-DD to the format DD MMM YYYY.

    Args:
    date: A string representing a date in the format YYYY-MM-DD.

    Returns:
    A string representing the date in the format DD MMM YYYY.
    """

    date = datetime.strptime(date, "%Y-%m-%d")
    return date.strftime("%d %B %Y")


def format_ordinal_numbers(input_date):

    if isinstance(input_date, datetime):
        date_obj = input_date
    else:
        # Parse the input date string
        date_obj = datetime.strptime(input_date, "%Y-%m-%d")

    # Get the day without leading zero and add the appropriate suffix
    day_number = int(date_obj.strftime("%d"))
    # Get the month name
    month_name = date_obj.strftime("%B")  # Use %B for full month name

    # Get the year
    year = date_obj.strftime("%Y")

    # Determine the ordinal suffix for the day
    if 10 <= day_number % 100 <= 20:
        suffix = "th"
    else:
        suffix = {1: "st", 2: "nd", 3: "rd"}.get(day_number % 10, "th")

    formatted_date = f"{day_number}{suffix} {month_name} {year}"
    return formatted_date


def format_date_for_excel_sorting(date):
    """Converts a date from the format YYYY-MM-DD to the format DD MMM YYYY.

    Args:
    date: A string representing a date in the format YYYY-MM-DD.

    Returns:
    A string representing the date in the format DD MMM YYYY.
    """

    return datetime.strptime(date, "%Y-%m-%d") if date else None


def format_date_to_iso(date_string):
    """
    Converts a date string in YYYY-MM-DD format to ISO format (YYYY-MM-DDTHH:MM:SS.mmmZ).
    """
    if date_string is not None and "":
        # Parse the date string
        date_obj = datetime.strptime(date_string, "%Y-%m-%d")

        # Create a new datetime object with desired time and microseconds
        desired_datetime = datetime(date_obj.year, date_obj.month, date_obj.day, 18, 30, 0, 0)

        # Format the datetime object in ISO format with milliseconds and timezone offset
        return desired_datetime.isoformat(sep="T", timespec="milliseconds") + "Z"


def convert_and_timestamp(date_string, max_time=True):
    """Converts a date string in YYYY-MM-DD format to a UTC timestamp.

    Args:
        date_string: The date string to be converted.

    Returns:
        The timestamp of the date in UTC.
    """

    date_obj = datetime.strptime(date_string, "%Y-%m-%d")
    if max_time:
        # Set the time to 23:59:59 (the maximum time for that date)
        date_obj = date_obj.replace(hour=23, minute=59, second=59)
    else:
        # Set the time to 00:00:00 (the minimum time for that date)
        date_obj = date_obj.replace(hour=0, minute=0, second=0)
    timestamp = date_obj.replace(tzinfo=timezone.utc).timestamp()
    return int(timestamp)


def convert_date_utc_format(date_string):
    utc_time = datetime.utcfromtimestamp(date_string)
    utc_time_str = utc_time.strftime("%m/%d/%Y %H:%M")
    return utc_time_str


from datetime import datetime


def format_date_from_numeric(input_date):
    """
    Converts a date string in the format 'dd/mm/yyyy' to 'Mon'YY'.

    Parameters:
        input_date (str): Date string in the format 'dd/mm/yyyy' (e.g., '30/11/2024').

    Returns:
        str: Formatted date string in the format 'Mon'YY' (e.g., 'Nov'24').
    """
    try:
        # Parse the input date string
        date_obj = datetime.strptime(input_date, "%d/%m/%Y")

        # Extract the short month name and last two digits of the year
        short_month = date_obj.strftime("%b")  # Abbreviated month name
        year_suffix = date_obj.strftime("%y")  # Last two digits of the year

        # Format and return the result
        formatted_date = f"{short_month}'{year_suffix}"
        return formatted_date

    except ValueError:
        raise ValueError("Invalid date format. Expected format: 'dd/mm/yyyy' .")
