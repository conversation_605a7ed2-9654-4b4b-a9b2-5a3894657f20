from collections import OrderedDict
from datetime import datetime
import logging

from bson import ObjectId
from flaskr.services.reporting_service import reporting_service
from flaskr.helpers.date_util import change_date_format
from flaskr.models import get_db
from flaskr.helpers.boto3_handler import upload_file
from celery_app.celery_holder import celery
from collections.abc import Generator
from flask import current_app as app


def transform_dates(client: str, data: Generator) -> Generator:
    for row in data:
        yield {
            **row,
            "bookingDate": datetime.strptime(row["bookingDate"], "%Y-%m-%d").strftime("%d-%m-%Y %H:%M:%S.%f")[:-3]
            if ((client == app.config.get("NAS") or client == app.config["FLYPOP"]) and row.get("bookingDate"))
            else change_date_format(row["bookingDate"]),
            "departureDate": datetime.strptime(row["departureDate"], "%Y-%m-%d").strftime("%d-%m-%Y %H:%M:%S.%f")[:-3]
            if ((client == app.config.get("NAS") or client == app.config["FLYPOP"]) and row.get("departureDate"))
            else change_date_format(row["departureDate"]),
            "returnDate": datetime.strptime(row["returnDate"], "%Y-%m-%d").strftime("%d-%m-%Y %H:%M:%S.%f")[:-3]
            if ((client == app.config.get("NAS") or client == app.config["FLYPOP"]) and row.get("returnDate"))
            else change_date_format(row["returnDate"]),
        }


@celery.task()
def trust_balance_report_task(data):
    client = data.get("client")
    file_id_xlsx = data.get("file_id_xlsx")
    file_id_csv = data.get("file_id_csv")
    if client == app.config.get("NAS") or client == app.config.get("FLYPOP"):
        client_basic_info = get_db().client_basic_info.find_one({"_id": ObjectId(client)})
        logging.info(f"client_basic_info: {client_basic_info}")
    if client == app.config.get("SWOOP") or client == app.config.get("SWOOP_TRAVEL"):
        details = reporting_service.multi_currency_trust_balance_report(data)
    # elif (client == app.config.get("NAS") or client == app.config.get("FLYPOP")) and client_basic_info.get(
    #     "new_workflow"
    # ):
    # TODO: Need to be changed later
    elif client == app.config.get("NAS") or client == app.config.get("FLYPOP"):
        logging.info("inside nas report>>>>>")
        details = reporting_service.trust_balance_report_nas(data)

        # Convert generator to list to ensure it's fully processed
        if isinstance(details["content"], Generator):
            details["content"] = list(details["content"])

        logging.info(f"nas report details: {details}")
    elif client == app.config.get("GTL"):
        details = reporting_service.trust_balance_report_gtl(data)
    else:
        logging.info("not inside nas report but else>>>>")
        details = reporting_service.trust_balance_report(data)

    # Ensure content is transformed
    details["content"] = list(transform_dates(client, details["content"]))

    header_dict = OrderedDict(
        [
            ("cId", "Client Id"),
            ("friendlyName", "Client Name"),
            ("bookingRef", "Booking Ref"),
            ("leadPax", "Lead Pax"),
            ("bookingDate", "Booking Date"),
            ("departureDate", "Date of Travel"),
            ("returnDate", "Date of Return"),
            ("supplierName", "Supplier Names"),
            ("currency", "Currency"),
            ("deposits", "Deposits"),
            ("refundsFromDepositFile", "Refund From Deposit File"),
            ("totalBanked", "Total Banked"),
            ("totalClaimed", "Total Claimed"),
            ("balance", "Balance"),
            ("status", "Status"),
            ("type", "Type"),
            ("totalBookingValue", "Total Booking Value"),
        ]
    )
    if (client not in [app.config.get("NAS"), app.config.get("FLYPOP")]) or (
        client in [app.config.get("NAS"), app.config.get("FLYPOP")] and not client_basic_info.get("new_workflow")
    ):
        header_dict.pop("supplierName")
    bucket = app.config["REPORTS_BUCKET"]

    logging.info("csv and xlsx file generation started")
    reporting_service.generate_csv_and_excel_report(header_dict, details["content"], file_id_csv, file_id_xlsx)
    logging.info("csv and xlsx file generation completed")

    logging.info("Uploading csv report to S3")
    upload_file(bucket=bucket, key=file_id_csv, file_path=f"{app.config['TEMP_DIR']}/{file_id_csv}")
    logging.info("Uploaded csv report to S3")

    logging.info("Uploading xlsx report to S3")
    upload_file(bucket=bucket, key=file_id_xlsx, file_path=f"{app.config['TEMP_DIR']}/{file_id_xlsx}")
    logging.info("Uploaded xlsx report to S3")

    now = datetime.utcnow()
    get_db().report_files.update_many(
        {"file_id": {"$in": [file_id_csv, file_id_xlsx]}},
        {
            "$set": {
                "status": "Generated New Report",
                "generated_at": now,
                "updated_at": now,
            }
        },
    )
    logging.info("Updated report information in db")
