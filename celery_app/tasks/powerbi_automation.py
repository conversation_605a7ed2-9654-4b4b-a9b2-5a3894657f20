from celery_app.celery_holder import celery
from flaskr.services.powerbi_service import powerbi_service
from flaskr.services.powerbi_data_service import powerbi_data_service
from flaskr.services.powerbi_automation_config import powerbi_automation_config
from flask import current_app as app
import logging
from datetime import datetime
import json

logger = logging.getLogger(__name__)


@celery.task()
def refresh_powerbi_reports_task(config):
    """
    Automated task to refresh PowerBI reports with latest banking and claims data
    
    Args:
        config (dict): Configuration containing:
            - report_ids (list): List of report IDs to refresh
            - client_ids (list): Optional list of client IDs to filter data
            - days_back (int): Number of days to look back for data (default: 30)
            - refresh_type (str): 'dataset_only', 'data_push', or 'full' (default: 'full')
            - workspace_id (str): Optional workspace ID override
    """
    try:
        logging.info("Starting PowerBI reports refresh automation")
        
        # Extract configuration
        report_ids = config.get("report_ids", [])
        client_ids = config.get("client_ids")
        days_back = config.get("days_back", 30)
        refresh_type = config.get("refresh_type", "full")
        workspace_id = config.get("workspace_id")
        
        if not report_ids:
            # Use default reports from config
            default_reports = []
            if app.config.get("POWERBI_REPORT_ID"):
                default_reports.append(app.config.get("POWERBI_REPORT_ID"))
            if app.config.get("POWERBI_ERV_REPORT_ID"):
                default_reports.append(app.config.get("POWERBI_ERV_REPORT_ID"))
            report_ids = default_reports
        
        if not report_ids:
            raise Exception("No report IDs provided and no default reports configured")
        
        results = []
        
        for report_id in report_ids:
            try:
                logging.info(f"Processing report: {report_id}")
                
                if refresh_type in ["data_push", "full"]:
                    # Extract and push fresh data
                    logging.info("Extracting banking data...")
                    banking_data = powerbi_data_service.get_latest_banking_data(
                        client_ids=client_ids, 
                        days_back=days_back
                    )
                    
                    logging.info("Extracting claims data...")
                    claims_data = powerbi_data_service.get_latest_claims_data(
                        client_ids=client_ids, 
                        days_back=days_back
                    )
                    
                    logging.info("Extracting summary data...")
                    summary_data = powerbi_data_service.get_combined_summary_data(
                        client_ids=client_ids, 
                        days_back=days_back
                    )
                    
                    # Update report with new data
                    update_result = powerbi_service.update_report_data(
                        report_id=report_id,
                        banking_data=banking_data,
                        claims_data=claims_data,
                        workspace_id=workspace_id
                    )
                    
                    # Also push summary data if available
                    if summary_data:
                        dataset_ids = powerbi_service.get_report_datasets(report_id, workspace_id)
                        for dataset_id in dataset_ids:
                            try:
                                powerbi_service.push_data_to_dataset(
                                    dataset_id, "SummaryData", summary_data, workspace_id
                                )
                            except Exception as e:
                                logging.warning(f"Failed to push summary data to dataset {dataset_id}: {str(e)}")
                    
                    results.append({
                        "report_id": report_id,
                        "status": "success",
                        "update_result": update_result,
                        "banking_records": len(banking_data),
                        "claims_records": len(claims_data),
                        "summary_records": len(summary_data),
                        "timestamp": datetime.utcnow().isoformat()
                    })
                    
                elif refresh_type == "dataset_only":
                    # Just refresh datasets without pushing new data
                    dataset_ids = powerbi_service.get_report_datasets(report_id, workspace_id)
                    refresh_results = []
                    
                    for dataset_id in dataset_ids:
                        refresh_result = powerbi_service.refresh_dataset(dataset_id, workspace_id)
                        refresh_results.append(refresh_result)
                    
                    results.append({
                        "report_id": report_id,
                        "status": "success",
                        "refresh_results": refresh_results,
                        "timestamp": datetime.utcnow().isoformat()
                    })
                
                logging.info(f"Successfully processed report: {report_id}")
                
            except Exception as e:
                error_msg = f"Failed to process report {report_id}: {str(e)}"
                logging.error(error_msg)
                results.append({
                    "report_id": report_id,
                    "status": "failed",
                    "error": error_msg,
                    "timestamp": datetime.utcnow().isoformat()
                })
        
        # Log final results
        successful_reports = [r for r in results if r["status"] == "success"]
        failed_reports = [r for r in results if r["status"] == "failed"]
        
        logging.info(f"PowerBI automation completed. Success: {len(successful_reports)}, Failed: {len(failed_reports)}")

        if failed_reports:
            logging.error(f"Failed reports: {json.dumps(failed_reports, indent=2)}")

        # Prepare final results
        final_results = {
            "total_reports": len(report_ids),
            "successful": len(successful_reports),
            "failed": len(failed_reports),
            "results": results,
            "completed_at": datetime.utcnow().isoformat()
        }

        # Log the automation run
        status = "completed" if len(failed_reports) == 0 else "failed"
        powerbi_automation_config.log_automation_run(config, final_results, status)

        return final_results
        
    except Exception as e:
        error_msg = f"PowerBI automation task failed: {str(e)}"
        logging.error(error_msg)
        raise Exception(error_msg)


@celery.task()
def refresh_single_report_task(report_id, workspace_id=None, client_ids=None, days_back=30):
    """
    Refresh a single PowerBI report
    
    Args:
        report_id (str): PowerBI report ID
        workspace_id (str): Optional workspace ID override
        client_ids (list): Optional list of client IDs to filter data
        days_back (int): Number of days to look back for data
    """
    try:
        logging.info(f"Starting refresh for single report: {report_id}")
        
        # Extract fresh data
        banking_data = powerbi_data_service.get_latest_banking_data(
            client_ids=client_ids, 
            days_back=days_back
        )
        
        claims_data = powerbi_data_service.get_latest_claims_data(
            client_ids=client_ids, 
            days_back=days_back
        )
        
        # Update report
        result = powerbi_service.update_report_data(
            report_id=report_id,
            banking_data=banking_data,
            claims_data=claims_data,
            workspace_id=workspace_id
        )
        
        logging.info(f"Successfully refreshed report: {report_id}")
        return {
            "report_id": report_id,
            "status": "success",
            "result": result,
            "banking_records": len(banking_data),
            "claims_records": len(claims_data),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        error_msg = f"Failed to refresh report {report_id}: {str(e)}"
        logging.error(error_msg)
        raise Exception(error_msg)


@celery.task()
def monitor_powerbi_refresh_status_task(dataset_id, workspace_id=None, max_wait_minutes=30):
    """
    Monitor the status of a PowerBI dataset refresh
    
    Args:
        dataset_id (str): PowerBI dataset ID
        workspace_id (str): Optional workspace ID override
        max_wait_minutes (int): Maximum time to wait for refresh completion
    """
    try:
        logging.info(f"Monitoring refresh status for dataset: {dataset_id}")
        
        import time
        start_time = datetime.utcnow()
        max_wait_seconds = max_wait_minutes * 60
        
        while True:
            # Check refresh history to get latest status
            refresh_history = powerbi_service.get_refresh_history(
                dataset_id, workspace_id, top=1
            )
            
            if refresh_history:
                latest_refresh = refresh_history[0]
                status = latest_refresh.get("status", "Unknown")
                
                logging.info(f"Dataset {dataset_id} refresh status: {status}")
                
                if status in ["Completed", "Failed", "Cancelled"]:
                    return {
                        "dataset_id": dataset_id,
                        "final_status": status,
                        "refresh_details": latest_refresh,
                        "monitoring_duration": (datetime.utcnow() - start_time).total_seconds(),
                        "timestamp": datetime.utcnow().isoformat()
                    }
            
            # Check if we've exceeded max wait time
            elapsed_seconds = (datetime.utcnow() - start_time).total_seconds()
            if elapsed_seconds >= max_wait_seconds:
                logging.warning(f"Monitoring timeout for dataset {dataset_id} after {max_wait_minutes} minutes")
                return {
                    "dataset_id": dataset_id,
                    "final_status": "Timeout",
                    "monitoring_duration": elapsed_seconds,
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            # Wait before next check
            time.sleep(30)  # Check every 30 seconds
            
    except Exception as e:
        error_msg = f"Failed to monitor dataset {dataset_id}: {str(e)}"
        logging.error(error_msg)
        raise Exception(error_msg)
