All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html)


## [0.0.62] - 2025-06-19
### Added
# IMP25-92,93,95
- Added Dynamic Report Changes for Powerbi (IMP25-92,93,95)

## [0.0.61] - 2025-06-09
### Changed
# IMP25-XX
- Changed ERV report id in powerbi

## [0.0.60] - 2025-06-04
### Changed
# IMP25-XX
- Changed ERV report id in powerbi

## [0.0.59] - 2025-05-30
### Changed
# IMP25-XX
- Changed ERV report id in powerbi

## [0.0.58] - 2025-05-29
### Changed
# IMP25-XX
- Changed ERV report id in powerbi

## [0.0.57] - 2025-05-22
### Added
# IMP25-79
- Changes in auth service to default to ptt-user if added from cognito and to ptt-client if added using create client

## [0.0.56] - 2025-05-19
### Added
# IMP25-79
- Added erv specific powerbi config for getting erv powerbi dashboard

## [0.0.55] - 2025-05-13
### Fixed
# IMP25-66,67
- Fixed typo in preprod for powerbi

## [0.0.53] - 2025-05-13
### Added
# IMP25-66,67
- Added test cases for powerbi embed service api

## [0.0.50] - 2025-05-09
### Added
# IMP25-66,67
- Added powerbi embed service api

## [0.0.44] - 2025-04-21
### Added
# IMP25-51
- Added lambda call for all TBR
- Minor change in export

## [0.0.43] - 2025-04-21
### Added
# IMP25-60
- Added progress API

## [0.0.42] - 2025-04-16
### Fixed
# IMP25-50
- Fixed calculation for difference in bluestyle report export

## [0.0.41] - 2025-04-11
### Removed
# IMP25-51
- the deduction of 10 million in the agreed protected funds.

## [0.0.40] - 2025-04-01
### Added
#### Escrow changes for Anglia & Wst
- Changed escrow calculation logic for Anglia & Wst editing restrictions

## [0.0.35] - 2025-03-13
### Added
- Added Logic for barclays statement download report in existing api

## [0.0.35] - 2025-03-12
### Changed
- Changed response for weekly barclays

## [0.0.32] - 2025-03-04
### Added
- Bank Names List lookup api

## [0.0.29] - 2025-02-28
### Added
- Removed api for barclays weekly export report
- Integrated with existing api (fortnightly weekly report); added logic for Barclays weekly report

## [0.0.29] - 2025-02-25
### Added
- Added api for barclays weekly export report

## [0.0.26] - 2025-02-20
### Added
- Added api for barclays weekly report

## [0.0.16] - 2025-01-29
### Added
- Removed the function for deductable and non bonded amount
- Added non bond amount in risk exposure breakdown api

## [0.0.12] - 2025-01-24
### Added
- Added api for deductable and non bond amount
### Changed
- Changed get response for deductable amount 

## [0.0.10] - 2025-01-23
### Added
- Added decorators for erv dashboard graphs

## [0.0.8] - 2025-01-23
### Added
- Added decorator for ptt zech client

## [0.0.6] - 2025-01-23
### Added
- Added deductable amount API and service

## [0.0.2] - 2025-02-04
### Added
- Added Non-trust booking view API and service
- Made changes in the download part

## [0.0.3] - 2025-01-08
### Added
- Blue style excel report

## [0.0.3] - 2024-12-19
### Added
- Adding user to cognito group

## [0.0.4] - 2024-12-19
### Fixed 
- risk_exposure_graph api

## [0.0.1] - 2024-12-19
### Added
- Change log and version properties.


## [0.0.5] - 2025-04-29
### Added
- Added SFTP logic into Barclays banking summary.
- Changed file format to xlsx.
- Refactored banking summary for barclays
- Changed export bank recon


## [0.0.1] - 2025-04-28
### Fixed
- FIxed pytest(Two of the tbr apis test are skipped for now, 
and as soon as the tbr issues are resolved it can be fixed)
