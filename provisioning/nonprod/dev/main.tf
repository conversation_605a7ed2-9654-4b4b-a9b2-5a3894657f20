module "ptt-service-tf-resources" {
  source                 = "../../common"
  env                    = "dev"
  app_version            = var.app_version
  secret_name            = var.secret_name
  ecs_task_desired_count = 2
  certificate_arn        = "arn:aws:acm:eu-west-2:829169657987:certificate/1fb0bd6e-c3ab-40de-a7a7-c746c95ab2b7"
  app_image              = "829169657987.dkr.ecr.eu-west-2.amazonaws.com/ptt-backend-service"
  vpc_cidr               = "10.0.0.0/16"
  public_cidrs = [
    "10.0.1.0/24",
    "10.0.2.0/24"
  ]
  private_cidrs = [
    "10.0.3.0/24",
    "10.0.4.0/24"
  ]
  major_travel     = "62fc883e8e261fdc4ac5098e"
  anglia_tours     = "64db7a65928bbf01f031945d"
  wst_travel       = "6560ae5f52ab3d286735999f"
  we_love_holidays = "62cc1b5116133e88d93dba5c"
  swoop            = "64099667c12e4ac934d38b55"
  swoop_travel     = "633169abc09804a1acb82289"
  nas              = "633e6670102c602adb09c933"
  flypop           = "62fc883e8e261fdc4ac50991"
  barrhead         = "62d918bdd541e0b8f6ba7942"
  hays             = "62d918bdd541e0b8f6ba7941"
  gtl              = "64c28fd221a0c828b14ff635"
  sunshine         = "62d918bdd541e0b8f6ba7944"
  broadway         = "63182622cb5c169582cbab52"
  iglu_escrow      = "64e738ed0fe12bb34229275a"
  caledonian       = "64d9eb84313960553aff0096"
  ts               = "64f204968741c6c43954a306"
  itgp             = "62d914a7165ccb6ff35708e8"
  inte             = "62d914a7165ccb6ff35708ee"
  turq             = "64bfbf515fbed2a12be3f567"
  hobe             = "64e62ac557fef8e95103a7a5"
  travel_republic  = "62cc1b5116133e88d93dba55"
  tct              = "62cc1b5116133e88d93dba49"
  tdc              = "63a40eaa519879c3cae39510"
  nst              = "62cc1b5116133e88d93dba4f"
  est              = "62cc1b5116133e88d93dba50"
  pgl              = "62cc1b5116133e88d93dba4e"
  wlh_new          = "66050a762ad12fbcfc113bc9"
  pennywood        = "65e9566ed324a4c7c6d807a5"
  bluestyle        = "667cdb7bccace8fadf9e6609"
  kafka_bootstrap_servers = var.kafka_bootstrap_servers
  powerbi_client_id     = "75d7eea9-737c-41fd-a451-dd70ca718a32"
  powerbi_client_secret = "****************************************"
  powerbi_tenant_id     = "d34e4eb1-2483-4c33-9da4-db24c02d41fb"
  powerbi_workspace_id  = "fc005dca-a7cf-44a2-943f-419fdf4ba4b2"
  powerbi_report_id     = "6f648524-b7c1-4b25-afa2-7a664a1b80b3"
  powerbi_erv_report_id     = "28f33c7c-4e6d-4938-a2df-2fcb4da4e380"
}

resource "aws_api_gateway_account" "ptt-api-gw-account" {
  cloudwatch_role_arn = aws_iam_role.cloudwatch.arn
}

resource "aws_iam_role" "cloudwatch" {
  name = "api_gateway_cloudwatch_global_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Service": "apigateway.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "cloudwatch" {
  name = "api_gateway_cloudwatch_global_policy"
  role = aws_iam_role.cloudwatch.id

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:DescribeLogGroups",
                "logs:DescribeLogStreams",
                "logs:PutLogEvents",
                "logs:GetLogEvents",
                "logs:FilterLogEvents"
            ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_policy" "ses_email" {
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ses:*"
      ],
      "Resource": "*"
    }
  ]
}
EOF
}

resource "aws_iam_user" "ses_user_1" {
  name = "ses-email-user"
}

resource "aws_iam_user_policy_attachment" "ses_attachment" {
  user       = aws_iam_user.ses_user_1.name
  policy_arn = aws_iam_policy.ses_email.arn
}
